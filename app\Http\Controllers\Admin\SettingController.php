<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        // For now, provide default settings since we might not have a settings table
        $settings = [
            'site_name' => 'PrintOnline Ghana',
            'site_tagline' => 'On-Demand Printing & Delivery',
            'site_description' => 'The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.',
            'currency' => 'GHS',
            'timezone' => 'Africa/Accra',
            'business_name' => 'PrintOnline Ghana',
            'business_phone' => '+233 24 123 4567',
            'business_email' => '<EMAIL>',
            'business_website' => 'https://printonlinegh.com',
            'business_address' => '123 Oxford Street, Osu, Accra, Ghana',
            'mail_from_name' => 'PrintOnline Ghana',
            'mail_from_address' => '<EMAIL>',
            'email_notifications' => true,
            'enable_paystack' => false,
            'enable_momo' => false,
            'enable_bank_transfer' => true,
            'shipping_fee_accra' => '15.00',
            'shipping_fee_other' => '25.00',
            'free_shipping_threshold' => '100.00',
            'processing_time' => '3',
        ];

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $settings = $request->input('settings', []);

        foreach ($settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                $setting->value = $value;
                $setting->save();
            }
        }

        return redirect()
            ->route('admin.settings.index')
            ->with('success', 'Settings updated successfully.');
    }
}
