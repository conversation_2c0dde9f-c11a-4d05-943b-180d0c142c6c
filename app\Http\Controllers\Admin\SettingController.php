<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        // Load settings from database with defaults
        $defaultSettings = [
            'site_name' => 'PrintOnline Ghana',
            'site_tagline' => 'On-Demand Printing & Delivery',
            'site_description' => 'The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.',
            'currency' => 'GHS',
            'timezone' => 'Africa/Accra',
            'business_name' => 'PrintOnline Ghana',
            'business_phone' => '+233 24 123 4567',
            'business_email' => '<EMAIL>',
            'business_website' => 'https://printonlinegh.com',
            'business_address' => '123 Oxford Street, Osu, Accra, Ghana',
            'mail_from_name' => 'PrintOnline Ghana',
            'mail_from_address' => '<EMAIL>',
            'email_notifications' => true,
            'enable_paystack' => false,
            'enable_momo' => false,
            'enable_bank_transfer' => true,
            'shipping_fee_accra' => '15.00',
            'shipping_fee_other' => '25.00',
            'free_shipping_threshold' => '100.00',
            'processing_time' => '3',
        ];

        // Get settings from database
        $dbSettings = Setting::pluck('value', 'key')->toArray();

        // Merge with defaults
        $settings = array_merge($defaultSettings, $dbSettings);

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'business_email' => 'required|email|max:255',
            'business_phone' => 'nullable|string|max:20',
            'shipping_fee_accra' => 'nullable|numeric|min:0',
            'shipping_fee_other' => 'nullable|numeric|min:0',
            'free_shipping_threshold' => 'nullable|numeric|min:0',
            'processing_time' => 'nullable|integer|min:1',
        ]);

        try {
            // Get all form inputs except CSRF token and method
            $inputs = $request->except(['_token', '_method']);

            foreach ($inputs as $key => $value) {
                // Handle checkboxes (they won't be in the request if unchecked)
                if (in_array($key, ['email_notifications', 'enable_paystack', 'enable_momo', 'enable_bank_transfer'])) {
                    $value = $request->has($key) ? '1' : '0';
                }

                // Update or create setting
                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $value,
                        'type' => $this->getSettingType($value),
                        'group' => $this->getSettingGroup($key),
                        'label' => $this->getSettingLabel($key),
                    ]
                );
            }

            return redirect()
                ->route('admin.settings.index')
                ->with('success', 'Settings updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()]);
        }
    }

    /**
     * Get the type of a setting value
     */
    private function getSettingType($value): string
    {
        if (is_bool($value) || in_array($value, ['0', '1', 'true', 'false'])) {
            return 'boolean';
        }

        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? 'float' : 'integer';
        }

        return 'string';
    }

    /**
     * Get the group for a setting key
     */
    private function getSettingGroup(string $key): string
    {
        if (str_starts_with($key, 'business_')) {
            return 'business';
        }

        if (str_starts_with($key, 'mail_') || str_contains($key, 'email')) {
            return 'email';
        }

        if (str_contains($key, 'payment') || str_contains($key, 'paystack') || str_contains($key, 'momo')) {
            return 'payment';
        }

        if (str_contains($key, 'shipping') || str_contains($key, 'processing')) {
            return 'shipping';
        }

        return 'general';
    }

    /**
     * Get a human-readable label for a setting key
     */
    private function getSettingLabel(string $key): string
    {
        return ucwords(str_replace('_', ' ', $key));
    }
}
