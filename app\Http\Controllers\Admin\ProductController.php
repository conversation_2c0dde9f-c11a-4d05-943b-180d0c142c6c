<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductOption;
use App\Models\ProductOptionValue;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(Request $request)
    {
        $query = Product::with(['options.values']);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%')
                  ->orWhere('category', 'like', '%' . $search . '%');
            });
        }

        // Filter by category
        if ($request->has('category') && !empty($request->category)) {
            $query->where('category', $request->category);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $products = $query->orderBy('sort_order')->orderBy('name')->paginate(20);

        $categories = Product::select('category')->distinct()->orderBy('category')->pluck('category');

        // Calculate statistics
        $activeCount = Product::where('is_active', true)->count();
        $categoriesCount = Product::select('category')->distinct()->count();
        $featuredCount = Product::where('is_featured', true)->count();

        return view('admin.products.index', compact('products', 'categories', 'activeCount', 'categoriesCount', 'featuredCount'));
    }

    /**
     * Show the form for creating a new product
     */
    public function create()
    {
        return view('admin.products.create');
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'options' => 'nullable|array',
            'options.*.name' => 'required_with:options|string|max:255',
            'options.*.type' => 'nullable|string|in:button,color,quantity',
            'options.*.values' => 'nullable|array',
            'options.*.values.*.name' => 'required_with:options.*.values|string|max:255',
            'options.*.values.*.value' => 'nullable|string|max:255',
            'options.*.values.*.price' => 'nullable|numeric|min:0',
            'options.*.values.*.multiplier' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            $product = Product::create([
                'name' => $request->name,
                'slug' => $request->slug ?: Str::slug($request->name),
                'description' => $request->description,
                'category' => $request->category,
                'is_best_seller' => $request->boolean('is_best_seller'),
                'is_featured' => $request->boolean('is_featured'),
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->integer('sort_order', 0),
            ]);

            // Handle image upload
            if ($request->hasFile('image')) {
                $product->addMediaFromRequest('image')
                    ->toMediaCollection('images');
            }

            // Handle product options
            if ($request->has('options')) {
                $this->syncProductOptions($product, $request->options);
            }

            DB::commit();

            return redirect()
                ->route('admin.products.show', $product)
                ->with('success', 'Product created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create product: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        // Get recent orders for this product
        $recentOrders = Order::whereHas('items', function($query) use ($product) {
            $query->where('product_id', $product->id);
        })->with('user')->latest()->take(5)->get();

        // Calculate statistics
        $totalOrders = Order::whereHas('items', function($query) use ($product) {
            $query->where('product_id', $product->id);
        })->count();

        $totalRevenue = Order::whereHas('items', function($query) use ($product) {
            $query->where('product_id', $product->id);
        })->where('status', '!=', 'cancelled')->sum('total_amount');

        $monthlyOrders = Order::whereHas('items', function($query) use ($product) {
            $query->where('product_id', $product->id);
        })->where('created_at', '>=', now()->startOfMonth())->count();

        $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;

        return view('admin.products.show', compact(
            'product',
            'recentOrders',
            'totalOrders',
            'totalRevenue',
            'monthlyOrders',
            'avgOrderValue'
        ));
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit(Product $product)
    {
        $product->load(['options.values']);
        
        return view('admin.products.edit', compact('product'));
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'options' => 'nullable|array',
            'options.*.name' => 'required_with:options|string|max:255',
            'options.*.type' => 'nullable|string|in:button,color,quantity',
            'options.*.values' => 'nullable|array',
            'options.*.values.*.name' => 'required_with:options.*.values|string|max:255',
            'options.*.values.*.value' => 'nullable|string|max:255',
            'options.*.values.*.price' => 'nullable|numeric|min:0',
            'options.*.values.*.multiplier' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            $product->update([
                'name' => $request->name,
                'slug' => $request->slug ?: Str::slug($request->name),
                'description' => $request->description,
                'category' => $request->category,
                'is_best_seller' => $request->boolean('is_best_seller'),
                'is_featured' => $request->boolean('is_featured'),
                'is_active' => $request->boolean('is_active'),
                'sort_order' => $request->integer('sort_order', 0),
            ]);

            // Handle image upload
            if ($request->hasFile('image')) {
                $product->clearMediaCollection('images');
                $product->addMediaFromRequest('image')
                    ->toMediaCollection('images');
            }

            // Handle product options
            $this->syncProductOptions($product, $request->options ?? []);

            DB::commit();

            return redirect()
                ->route('admin.products.show', $product)
                ->with('success', 'Product updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update product: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified product (soft delete)
     */
    public function destroy(Product $product)
    {
        // Soft delete the product - this preserves order history
        $product->delete();

        return redirect()
            ->route('admin.products.index')
            ->with('success', 'Product deleted successfully. Order history is preserved.');
    }

    /**
     * Sync product options and their values
     */
    private function syncProductOptions(Product $product, array $options)
    {
        // Delete existing options and their values
        $product->options()->delete();

        foreach ($options as $index => $optionData) {
            if (empty($optionData['name'])) {
                continue;
            }

            $option = $product->options()->create([
                'name' => $optionData['name'],
                'type' => $optionData['type'] ?? 'button',
                'sort_order' => $index,
            ]);

            if (!empty($optionData['values'])) {
                foreach ($optionData['values'] as $valueIndex => $valueData) {
                    if (empty($valueData['name'])) {
                        continue;
                    }

                    $option->values()->create([
                        'name' => $valueData['name'],
                        'value' => $valueData['value'] ?? null,
                        'price' => $valueData['price'] ?? 0,
                        'multiplier' => $valueData['multiplier'] ?? 1,
                        'sort_order' => $valueIndex,
                    ]);
                }
            }
        }
    }
}
