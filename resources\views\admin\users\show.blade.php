<x-layouts.admin title="User Details">
    <x-slot name="pageTitle">{{ $user->name }}</x-slot>
    <x-slot name="pageDescription">View user profile and order history</x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Profile Card -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="text-center">
                    <div class="w-20 h-20 rounded-full flex items-center justify-center font-bold text-xl mx-auto mb-4"
                         style="background-color: {{ $user->avatar_color ?? '#E5E7EB' }}; color: {{ $user->avatar_text_color ?? '#374151' }}">
                        {{ $user->avatar_initials ?? strtoupper(substr($user->name, 0, 2)) }}
                    </div>
                    
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $user->name }}</h3>
                    
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                        {{ $user->role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800' }}">
                        {{ ucfirst($user->role) }}
                    </span>
                </div>
                
                <div class="mt-6 space-y-3">
                    <div class="flex items-center text-sm">
                        <i class="fas fa-envelope text-gray-400 w-5"></i>
                        <span class="ml-3 text-gray-600">{{ $user->email }}</span>
                    </div>
                    
                    @if($user->phone)
                        <div class="flex items-center text-sm">
                            <i class="fas fa-phone text-gray-400 w-5"></i>
                            <span class="ml-3 text-gray-600">{{ $user->phone }}</span>
                        </div>
                    @endif
                    
                    @if($user->address)
                        <div class="flex items-start text-sm">
                            <i class="fas fa-map-marker-alt text-gray-400 w-5 mt-0.5"></i>
                            <span class="ml-3 text-gray-600">{{ $user->address }}</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center text-sm">
                        <i class="fas fa-calendar text-gray-400 w-5"></i>
                        <span class="ml-3 text-gray-600">Joined {{ $user->created_at->format('M j, Y') }}</span>
                    </div>
                    
                    @if($user->email_verified_at)
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check-circle text-green-500 w-5"></i>
                            <span class="ml-3 text-green-600">Email Verified</span>
                        </div>
                    @else
                        <div class="flex items-center text-sm">
                            <i class="fas fa-exclamation-circle text-yellow-500 w-5"></i>
                            <span class="ml-3 text-yellow-600">Email Not Verified</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- User Statistics -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Orders:</span>
                        <span class="text-lg font-bold text-gray-900">{{ $user->orders->count() }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Spent:</span>
                        <span class="text-lg font-bold text-green-600">
                            ₵{{ number_format($user->orders->where('status', '!=', 'cancelled')->sum('total_amount'), 2) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Completed Orders:</span>
                        <span class="text-lg font-bold text-blue-600">{{ $user->orders->where('status', 'completed')->count() }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Pending Orders:</span>
                        <span class="text-lg font-bold text-yellow-600">{{ $user->orders->whereIn('status', ['pending', 'confirmed', 'printing'])->count() }}</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            @if($user->id !== auth()->id())
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="mailto:{{ $user->email }}" 
                           class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                            <i class="fas fa-envelope mr-2"></i>Send Email
                        </a>
                        
                        <button onclick="toggleUserRole({{ $user->id }}, '{{ $user->role }}')"
                                class="block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition text-center">
                            <i class="fas fa-user-cog mr-2"></i>
                            {{ $user->role === 'admin' ? 'Remove Admin' : 'Make Admin' }}
                        </button>
                    </div>
                </div>
            @endif
        </div>

        <!-- Order History -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Order History</h3>
                    <span class="text-sm text-gray-500">{{ $user->orders->count() }} orders total</span>
                </div>
                
                @if($user->orders->count() > 0)
                    <div class="space-y-4">
                        @foreach($user->orders->sortByDesc('created_at') as $order)
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition">
                                <div class="flex items-center justify-between mb-3">
                                    <div>
                                        <h4 class="font-semibold text-gray-900">#{{ $order->order_number }}</h4>
                                        <p class="text-sm text-gray-500">{{ $order->created_at->format('M j, Y g:i A') }}</p>
                                    </div>
                                    
                                    <div class="text-right">
                                        <p class="font-semibold text-gray-900">{{ $order->formatted_total }}</p>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                                            @elseif($order->status === 'printing') bg-purple-100 text-purple-800
                                            @elseif($order->status === 'completed') bg-green-100 text-green-800
                                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </div>
                                </div>
                                
                                @if($order->items->count() > 0)
                                    <div class="text-sm text-gray-600 mb-3">
                                        <strong>Items:</strong>
                                        @foreach($order->items as $item)
                                            {{ $item->product_name }} ({{ $item->quantity }}x){{ !$loop->last ? ', ' : '' }}
                                        @endforeach
                                    </div>
                                @endif
                                
                                @if($order->notes)
                                    <div class="text-sm text-gray-600 mb-3">
                                        <strong>Notes:</strong> {{ $order->notes }}
                                    </div>
                                @endif
                                
                                <div class="flex justify-end">
                                    <a href="{{ route('admin.orders.show', $order) }}" 
                                       class="text-pink-600 hover:text-pink-800 text-sm font-medium">
                                        View Details <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-shopping-bag text-4xl text-gray-300 mb-4"></i>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">No Orders Yet</h4>
                        <p class="text-gray-600">This user hasn't placed any orders yet.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-layouts.admin>

@push('scripts')
<script>
function toggleUserRole(userId, currentRole) {
    const newRole = currentRole === 'admin' ? 'customer' : 'admin';
    const action = currentRole === 'admin' ? 'remove admin privileges from' : 'grant admin privileges to';
    
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}/toggle-role`;
        
        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // Add method override
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PATCH';
        form.appendChild(methodInput);
        
        // Add role input
        const roleInput = document.createElement('input');
        roleInput.type = 'hidden';
        roleInput.name = 'role';
        roleInput.value = newRole;
        form.appendChild(roleInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
