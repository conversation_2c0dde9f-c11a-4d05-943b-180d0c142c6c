<x-layouts.admin title="Designer Details">
    <x-slot name="pageTitle">Designer: {{ $designer->name }}</x-slot>
    <x-slot name="pageDescription">View designer profile details</x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Designer Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Designer Profile -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Designer Profile</h3>
                
                <div class="flex items-start space-x-6">
                    <img src="{{ $designer->image_url }}" 
                         alt="{{ $designer->name }}" 
                         class="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg">
                    
                    <div class="flex-1">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $designer->name }}</h2>
                        <p class="text-gray-600 leading-relaxed mb-4">{{ $designer->bio }}</p>
                        
                        @if($designer->portfolio_url)
                            <a href="{{ $designer->portfolio_url }}" 
                               target="_blank"
                               class="inline-flex items-center px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition">
                                <i class="fas fa-external-link-alt mr-2"></i>View Portfolio
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Social Links -->
            @if($designer->social_links && count(array_filter($designer->social_links)) > 0)
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Social Links</h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        @if(!empty($designer->social_links['behance']))
                            <a href="{{ $designer->social_links['behance'] }}" 
                               target="_blank"
                               class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition">
                                <i class="fab fa-behance text-2xl text-blue-600 mr-2"></i>
                                <span class="font-medium text-gray-700">Behance</span>
                            </a>
                        @endif
                        
                        @if(!empty($designer->social_links['dribbble']))
                            <a href="{{ $designer->social_links['dribbble'] }}" 
                               target="_blank"
                               class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-pink-300 hover:bg-pink-50 transition">
                                <i class="fab fa-dribbble text-2xl text-pink-600 mr-2"></i>
                                <span class="font-medium text-gray-700">Dribbble</span>
                            </a>
                        @endif
                        
                        @if(!empty($designer->social_links['instagram']))
                            <a href="{{ $designer->social_links['instagram'] }}" 
                               target="_blank"
                               class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition">
                                <i class="fab fa-instagram text-2xl text-purple-600 mr-2"></i>
                                <span class="font-medium text-gray-700">Instagram</span>
                            </a>
                        @endif
                        
                        @if(!empty($designer->social_links['linkedin']))
                            <a href="{{ $designer->social_links['linkedin'] }}" 
                               target="_blank"
                               class="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:border-blue-700 hover:bg-blue-50 transition">
                                <i class="fab fa-linkedin text-2xl text-blue-700 mr-2"></i>
                                <span class="font-medium text-gray-700">LinkedIn</span>
                            </a>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Designer Page Preview -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Designers Page Preview</h3>
                <p class="text-gray-600 mb-4">This is how the designer appears on the public designers page:</p>
                
                <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
                    <div class="text-center">
                        <img src="{{ $designer->image_url }}" 
                             alt="{{ $designer->name }}" 
                             class="w-32 h-32 rounded-full mx-auto mb-6 object-cover border-4 border-white shadow-lg">
                        
                        <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $designer->name }}</h3>
                        <p class="text-gray-500 mb-6 leading-relaxed">{{ $designer->bio }}</p>
                        
                        @if($designer->social_links && count(array_filter($designer->social_links)) > 0)
                            <div class="flex justify-center space-x-4">
                                @if(!empty($designer->social_links['behance']))
                                    <a href="#" class="text-blue-600 hover:text-blue-800 transition">
                                        <i class="fab fa-behance text-xl"></i>
                                    </a>
                                @endif
                                @if(!empty($designer->social_links['dribbble']))
                                    <a href="#" class="text-pink-600 hover:text-pink-800 transition">
                                        <i class="fab fa-dribbble text-xl"></i>
                                    </a>
                                @endif
                                @if(!empty($designer->social_links['instagram']))
                                    <a href="#" class="text-purple-600 hover:text-purple-800 transition">
                                        <i class="fab fa-instagram text-xl"></i>
                                    </a>
                                @endif
                                @if(!empty($designer->social_links['linkedin']))
                                    <a href="#" class="text-blue-700 hover:text-blue-900 transition">
                                        <i class="fab fa-linkedin text-xl"></i>
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Status:</span>
                        <span class="font-semibold {{ $designer->is_active ? 'text-green-600' : 'text-red-600' }}">
                            {{ $designer->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Sort Order:</span>
                        <span class="font-semibold">{{ $designer->sort_order }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Bio Length:</span>
                        <span class="font-semibold">{{ strlen($designer->bio) }}/500</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Social Links:</span>
                        <span class="font-semibold">{{ count(array_filter($designer->social_links ?? [])) }}</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.designers.edit', $designer) }}" 
                       class="block w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition text-center">
                        <i class="fas fa-edit mr-2"></i>Edit Designer
                    </a>
                    
                    <a href="{{ route('designers') }}" 
                       target="_blank"
                       class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                        <i class="fas fa-external-link-alt mr-2"></i>View Public Page
                    </a>
                    
                    <a href="{{ route('admin.designers.index') }}" 
                       class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Designers
                    </a>
                    
                    <form method="POST" action="{{ route('admin.designers.destroy', $designer) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this designer? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition">
                            <i class="fas fa-trash mr-2"></i>Delete Designer
                        </button>
                    </form>
                </div>
            </div>

            <!-- Designer Info -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Designer Information</h3>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <span class="text-gray-600">Created:</span>
                        <span class="font-medium">{{ $designer->created_at->format('F j, Y') }}</span>
                    </div>
                    @if($designer->updated_at != $designer->created_at)
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <span class="font-medium">{{ $designer->updated_at->format('F j, Y') }}</span>
                        </div>
                    @endif
                    @if($designer->portfolio_url)
                        <div>
                            <span class="text-gray-600">Portfolio:</span>
                            <a href="{{ $designer->portfolio_url }}" 
                               target="_blank"
                               class="font-medium text-pink-600 hover:text-pink-800 break-all">
                                {{ Str::limit($designer->portfolio_url, 30) }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-layouts.admin>
