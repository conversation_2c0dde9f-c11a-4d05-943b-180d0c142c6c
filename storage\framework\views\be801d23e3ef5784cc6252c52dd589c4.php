<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => ['title' => $product->name . ' - PrintOnline Ghana']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product->name . ' - PrintOnline Ghana')]); ?>
    <div class="bg-white min-h-screen">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Breadcrumbs -->
            <nav class="text-sm text-gray-500 mb-8">
                <a href="<?php echo e(route('home')); ?>" class="hover:text-gray-800">Home</a>
                <span class="mx-2">/</span>
                <a href="<?php echo e(route('shop')); ?>?category=<?php echo e($product->category); ?>" class="hover:text-gray-800"><?php echo e($product->category); ?></a>
                <span class="mx-2">/</span>
                <span class="font-medium text-gray-800"><?php echo e($product->name); ?></span>
            </nav>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Left Column: Product Image -->
                <div class="bg-orange-200 rounded-2xl flex items-center justify-center p-8">
                    <img id="product-image" 
                         src="<?php echo e($product->image_url); ?>" 
                         alt="<?php echo e($product->name); ?>" 
                         class="max-w-full h-auto">
                </div>

                <!-- Right Column: Product Details -->
                <div>
                    <h1 class="text-4xl font-bold"><?php echo e($product->name); ?></h1>
                    <p class="text-gray-500 mt-2">By PrintOnline Ghana</p>
                    
                    <div class="flex items-center mt-4">
                        <p id="product-price" class="text-3xl font-bold text-orange-500">
                            <?php echo e($defaultPricing['formatted_price']); ?>

                        </p>
                    </div>

                    <p class="mt-6 text-gray-600"><?php echo e($product->description); ?></p>

                    <form id="order-form" method="POST" action="<?php echo e(route('orders.store')); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="product_id" value="<?php echo e($product->id); ?>">

                        <!-- Product Options -->
                        <div id="product-options" class="mt-8 space-y-6">
                            <?php $__currentLoopData = $product->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div>
                                    <h3 class="text-md font-semibold text-gray-800 mb-3"><?php echo e($option->name); ?></h3>
                                    <div class="flex items-center gap-3 flex-wrap">
                                        <?php $__currentLoopData = $option->values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $valueIndex => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($option->type === 'color'): ?>
                                                <button type="button" 
                                                        class="option-btn w-6 h-6 rounded-full border-2 <?php echo e($valueIndex === 0 ? 'selected ring-2 ring-gray-800' : 'border-gray-300'); ?>"
                                                        style="background-color: <?php echo e($value->value); ?>"
                                                        data-option-id="<?php echo e($option->id); ?>"
                                                        data-value-id="<?php echo e($value->id); ?>"
                                                        data-option-index="<?php echo e($optionIndex); ?>"
                                                        data-value-index="<?php echo e($valueIndex); ?>"
                                                        title="<?php echo e($value->name); ?>">
                                                </button>
                                            <?php else: ?>
                                                <button type="button" 
                                                        class="option-btn <?php echo e($valueIndex === 0 ? 'selected' : ''); ?>"
                                                        data-option-id="<?php echo e($option->id); ?>"
                                                        data-value-id="<?php echo e($value->id); ?>"
                                                        data-option-index="<?php echo e($optionIndex); ?>"
                                                        data-value-index="<?php echo e($valueIndex); ?>">
                                                    <?php echo e($value->name); ?>

                                                </button>
                                            <?php endif; ?>
                                            <input type="hidden" 
                                                   name="options[<?php echo e($option->id); ?>]" 
                                                   value="<?php echo e($valueIndex === 0 ? $value->id : ''); ?>"
                                                   id="option-<?php echo e($option->id); ?>">
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Quantity -->
                        <div class="mt-6">
                            <h3 class="text-md font-semibold text-gray-800 mb-3">Quantity</h3>
                            <input type="number" 
                                   name="quantity" 
                                   value="1" 
                                   min="1" 
                                   max="100"
                                   class="quantity-input w-20 text-center border border-gray-300 rounded-lg py-2 px-3 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        </div>

                        <!-- File Upload -->
                        <div class="mt-8">
                            <h3 class="text-md font-semibold text-gray-800 mb-3">Upload Your Design</h3>
                            <div id="uppy-drag-drop"></div>
                            <div id="uppy-progress"></div>
                            <div id="uppy-status"></div>
                            <input type="file" 
                                   name="design_file" 
                                   accept="image/*,.pdf,.svg"
                                   class="mt-4 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100">
                            <p class="text-xs text-gray-500 mt-2">
                                Supported formats: JPG, PNG, GIF, PDF, SVG (Max: 10MB)
                            </p>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="flex items-center mt-8 gap-4">
                            <?php if(auth()->guard()->check()): ?>
                                <button type="submit" 
                                        class="flex-grow bg-orange-500 text-white font-bold py-4 rounded-lg hover:bg-orange-600 transition font-heading">
                                    Print
                                </button>
                            <?php else: ?>
                                <button type="button" 
                                        onclick="showAuthModal()"
                                        class="flex-grow bg-orange-500 text-white font-bold py-4 rounded-lg hover:bg-orange-600 transition font-heading">
                                    Print
                                </button>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Payment Methods -->
                        <div class="mt-8 border-t pt-6">
                            <h3 class="font-bold text-center">Guaranteed safe checkout</h3>
                            <div class="flex justify-center items-center gap-4 mt-4 text-3xl text-gray-400">
                                <i class="fab fa-cc-paypal"></i>
                                <i class="fab fa-cc-visa"></i>
                                <i class="fab fa-cc-mastercard"></i>
                                <i class="fab fa-cc-amex"></i>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Product data for JavaScript
        const productData = <?php echo json_encode([
            'id' => $product->id, 'name' => $product->name, 'options' => $product->options->map(function($option) {
                return [
                    'id' => $option->id) ?>;

        // Handle option selection
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('option-btn')) {
                const btn = e.target;
                const optionId = btn.dataset.optionId;
                const valueId = btn.dataset.valueId;
                
                // Remove selected class from siblings
                const siblings = btn.parentElement.querySelectorAll('.option-btn');
                siblings.forEach(sibling => {
                    sibling.classList.remove('selected');
                    if (sibling.classList.contains('ring-2')) {
                        sibling.classList.remove('ring-2', 'ring-gray-800');
                    }
                });
                
                // Add selected class to clicked button
                btn.classList.add('selected');
                if (btn.style.backgroundColor) { // Color option
                    btn.classList.add('ring-2', 'ring-gray-800');
                }
                
                // Update hidden input
                document.getElementById(`option-${optionId}`).value = valueId;
                
                // Update price
                updatePrice();
            }
        });

        // Update price function
        function updatePrice() {
            const selectedOptions = {};
            
            // Get all selected options
            productData.options.forEach(option => {
                const input = document.getElementById(`option-${option.id}`);
                if (input && input.value) {
                    selectedOptions[option.id] = parseInt(input.value);
                }
            });

            // Calculate price via API
            fetch(`<?php echo e(route('product.price', $product)); ?>`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    options: selectedOptions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('product-price').textContent = data.pricing.formatted_price;
                }
            })
            .catch(error => {
                console.error('Error calculating price:', error);
            });
        }

        // Initialize Uppy for file upload (optional enhancement)
        // This would require Uppy to be properly loaded
        
        // Form validation
        document.getElementById('order-form').addEventListener('submit', function(e) {
            // Check if all required options are selected
            let allSelected = true;
            productData.options.forEach(option => {
                const input = document.getElementById(`option-${option.id}`);
                if (!input.value) {
                    allSelected = false;
                }
            });

            if (!allSelected) {
                e.preventDefault();
                alert('Please select all product options before placing your order.');
                return false;
            }
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/product/show.blade.php ENDPATH**/ ?>