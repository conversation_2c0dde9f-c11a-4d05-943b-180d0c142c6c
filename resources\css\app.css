@import 'tailwindcss';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap');

/* Custom styles from prototype */
body {
    font-family: 'Lato', sans-serif;
    background-color: #f8f9fa;
}

h1, h2, h3, h4, h5, h6, .font-heading {
    font-family: 'Poppins', sans-serif;
}

.top-bar {
    border-bottom: 1px solid #e5e7eb;
}

.hero-product-card {
    background-color: #f8f9fa;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0,0,0,0.1), 0 8px 10px -6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.hero-product-card:hover {
    transform: translateY(-10px);
}

.product-card {
    background-color: #ffffff;
    border-radius: 1.5rem;
    padding: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.option-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.option-btn.selected {
    background-color: #111827;
    color: #fff;
    border-color: #111827;
}

/* Reviews Slider Styles */
@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

.reviews-slider {
    overflow: hidden;
    padding: 2rem 0;
    white-space: nowrap;
    position: relative;
}

.reviews-track {
    display: inline-block;
    animation: scroll 60s linear infinite;
}

.reviews-slider:hover .reviews-track {
    animation-play-state: paused;
}

.review-card {
    display: inline-block;
    width: 350px;
    background: #fff;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 0 1rem;
    white-space: normal;
    box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -2px rgba(0,0,0,0.1);
}
