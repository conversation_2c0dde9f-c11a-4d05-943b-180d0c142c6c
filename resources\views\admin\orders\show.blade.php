<x-layouts.admin title="Order Details">
    <x-slot name="pageTitle">Order #{{ $order->order_number }}</x-slot>
    <x-slot name="pageDescription">View and manage order details</x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Order Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Order Items -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Order Items</h3>
                
                <div class="space-y-4">
                    @foreach($order->items as $item)
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center space-x-4">
                                @if($item->product && $item->product->image_url)
                                    <img src="{{ $item->product->image_url }}" alt="{{ $item->product_name }}" 
                                         class="w-16 h-16 object-cover rounded-lg">
                                @else
                                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                @endif
                                
                                <div>
                                    <h4 class="font-semibold text-gray-900">{{ $item->product_name }}</h4>
                                    <p class="text-sm text-gray-600">{{ $item->formatted_options }}</p>
                                    <p class="text-sm text-gray-500">Quantity: {{ $item->quantity }}</p>
                                </div>
                            </div>
                            
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ $item->formatted_total_price }}</p>
                                <p class="text-sm text-gray-500">{{ $item->formatted_unit_price }} each</p>
                            </div>
                        </div>

                        <!-- Uploaded Files for this item -->
                        @if($item->getMedia('design_files')->count() > 0)
                            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                                <h5 class="font-medium text-gray-900 mb-3">
                                    <i class="fas fa-paperclip mr-2"></i>Uploaded Files ({{ $item->getMedia('design_files')->count() }})
                                </h5>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    @foreach($item->getMedia('design_files') as $file)
                                        <div class="relative group">
                                            <div class="aspect-square bg-white rounded-lg border-2 border-gray-200 overflow-hidden hover:border-pink-300 transition-colors">
                                                @if(str_starts_with($file->mime_type, 'image/'))
                                                    <img src="{{ $file->getUrl() }}"
                                                         alt="{{ $file->name }}"
                                                         class="w-full h-full object-cover">
                                                @else
                                                    <div class="w-full h-full flex flex-col items-center justify-center text-gray-400">
                                                        <i class="fas fa-file-alt text-3xl mb-2"></i>
                                                        <span class="text-xs text-center px-2">{{ pathinfo($file->name, PATHINFO_EXTENSION) }}</span>
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- File info overlay -->
                                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                <div class="flex space-x-2">
                                                    <a href="{{ $file->getUrl() }}"
                                                       target="_blank"
                                                       class="px-3 py-1 bg-white text-gray-800 rounded text-sm hover:bg-gray-100 transition">
                                                        <i class="fas fa-eye mr-1"></i>View
                                                    </a>
                                                    <a href="{{ route('admin.orders.download-file', ['order' => $order, 'media' => $file->id]) }}"
                                                       class="px-3 py-1 bg-pink-600 text-white rounded text-sm hover:bg-pink-700 transition">
                                                        <i class="fas fa-download mr-1"></i>Download
                                                    </a>
                                                </div>
                                            </div>

                                            <!-- File name -->
                                            <p class="mt-2 text-xs text-gray-600 truncate" title="{{ $file->name }}">
                                                {{ $file->name }}
                                            </p>
                                            <p class="text-xs text-gray-400">
                                                {{ $file->human_readable_size }}
                                            </p>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
                
                <!-- Order Total -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                        <span class="text-2xl font-bold text-pink-600">{{ $order->formatted_total }}</span>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Customer Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Customer Details</h4>
                        <p class="text-gray-600">{{ $order->user->name }}</p>
                        <p class="text-gray-600">{{ $order->user->email }}</p>
                        @if($order->user->phone)
                            <p class="text-gray-600">{{ $order->user->phone }}</p>
                        @endif
                    </div>
                    
                    @if($order->billing_address)
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Billing Address</h4>
                            <div class="text-gray-600 text-sm">
                                <p>{{ $order->billing_address['name'] ?? '' }}</p>
                                <p>{{ $order->billing_address['address'] ?? '' }}</p>
                                <p>{{ $order->billing_address['city'] ?? '' }}, {{ $order->billing_address['region'] ?? '' }}</p>
                                @if(isset($order->billing_address['phone']))
                                    <p>{{ $order->billing_address['phone'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                    
                    @if($order->shipping_address && $order->shipping_address !== $order->billing_address)
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Shipping Address</h4>
                            <div class="text-gray-600 text-sm">
                                <p>{{ $order->shipping_address['name'] ?? '' }}</p>
                                <p>{{ $order->shipping_address['address'] ?? '' }}</p>
                                <p>{{ $order->shipping_address['city'] ?? '' }}, {{ $order->shipping_address['region'] ?? '' }}</p>
                                @if(isset($order->shipping_address['phone']))
                                    <p>{{ $order->shipping_address['phone'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Order Notes -->
            @if($order->notes)
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Notes</h3>
                    <p class="text-gray-600">{{ $order->notes }}</p>
                </div>
            @endif
        </div>

        <!-- Order Status & Actions -->
        <div class="space-y-6">
            <!-- Order Status -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Status</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Current Status:</span>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                            @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                            @elseif($order->status === 'printing') bg-purple-100 text-purple-800
                            @elseif($order->status === 'completed') bg-green-100 text-green-800
                            @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                            @else bg-gray-100 text-gray-800 @endif">
                            {{ ucfirst($order->status) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Order Date:</span>
                        <span class="text-sm text-gray-900">{{ $order->created_at->format('M j, Y g:i A') }}</span>
                    </div>
                    
                    @if($order->confirmed_at)
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Confirmed:</span>
                            <span class="text-sm text-gray-900">{{ $order->confirmed_at->format('M j, Y g:i A') }}</span>
                        </div>
                    @endif
                    
                    @if($order->completed_at)
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Completed:</span>
                            <span class="text-sm text-gray-900">{{ $order->completed_at->format('M j, Y g:i A') }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Status Update Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Status</h3>
                
                <form method="POST" action="{{ route('admin.orders.update-status', $order) }}">
                    @csrf
                    @method('PATCH')
                    
                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                            <select name="status" id="status" 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="confirmed" {{ $order->status === 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                                <option value="printing" {{ $order->status === 'printing' ? 'selected' : '' }}>Printing</option>
                                <option value="completed" {{ $order->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                        </div>
                        
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            Update Status
                        </button>
                    </div>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.orders.index') }}" 
                       class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Orders
                    </a>
                    
                    <button onclick="window.print()" 
                            class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                        <i class="fas fa-print mr-2"></i>Print Order
                    </button>
                    
                    <a href="mailto:{{ $order->user->email }}?subject=Order {{ $order->order_number }}" 
                       class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition text-center">
                        <i class="fas fa-envelope mr-2"></i>Email Customer
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-layouts.admin>
