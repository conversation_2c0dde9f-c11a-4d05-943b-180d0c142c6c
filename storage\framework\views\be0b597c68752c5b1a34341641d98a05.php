<?php if (isset($component)) { $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.admin','data' => ['title' => 'Product Details']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.admin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Product Details']); ?>
     <?php $__env->slot('pageTitle', null, []); ?> <?php echo e($product->name); ?> <?php $__env->endSlot(); ?>
     <?php $__env->slot('pageDescription', null, []); ?> View product details and manage settings <?php $__env->endSlot(); ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Product Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Product Overview -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2"><?php echo e($product->name); ?></h3>
                        <div class="flex items-center gap-4">
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                                <?php echo e($product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e($product->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                            
                            <?php if($product->is_featured): ?>
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            <?php endif; ?>
                            
                            <span class="text-sm text-gray-500"><?php echo e($product->category); ?></span>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <p class="text-3xl font-bold text-pink-600">₵<?php echo e(number_format($product->base_price, 2)); ?></p>
                        <p class="text-sm text-gray-500">Base Price</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Product Image -->
                    <div>
                        <?php if($product->image_url): ?>
                            <img src="<?php echo e($product->image_url); ?>" alt="<?php echo e($product->name); ?>" 
                                 class="w-full h-64 object-cover rounded-lg border">
                        <?php else: ?>
                            <div class="w-full h-64 bg-gray-100 rounded-lg border flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Product Details -->
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600"><?php echo e($product->description ?: 'No description provided.'); ?></p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">Product Information</h4>
                            <dl class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">SKU:</dt>
                                    <dd class="text-gray-900"><?php echo e($product->sku ?? 'N/A'); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Category:</dt>
                                    <dd class="text-gray-900"><?php echo e($product->category); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Sort Order:</dt>
                                    <dd class="text-gray-900"><?php echo e($product->sort_order); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">URL Slug:</dt>
                                    <dd class="text-gray-900"><?php echo e($product->slug); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Created:</dt>
                                    <dd class="text-gray-900"><?php echo e($product->created_at->format('M j, Y')); ?></dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Last Updated:</dt>
                                    <dd class="text-gray-900"><?php echo e($product->updated_at->format('M j, Y')); ?></dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Recent Orders</h3>
                
                <?php if(isset($recentOrders) && $recentOrders->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                    <p class="font-semibold text-gray-900">#<?php echo e($order->order_number); ?></p>
                                    <p class="text-sm text-gray-600"><?php echo e($order->user->name); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($order->created_at->format('M j, Y')); ?></p>
                                </div>
                                
                                <div class="text-right">
                                    <p class="font-semibold text-gray-900">₵<?php echo e(number_format($order->total_amount, 2)); ?></p>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php if($order->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                        <?php elseif($order->status === 'confirmed'): ?> bg-blue-100 text-blue-800
                                        <?php elseif($order->status === 'printing'): ?> bg-purple-100 text-purple-800
                                        <?php elseif($order->status === 'completed'): ?> bg-green-100 text-green-800
                                        <?php elseif($order->status === 'cancelled'): ?> bg-red-100 text-red-800
                                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                        <?php echo e(ucfirst($order->status)); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-shopping-bag text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg">No Orders Yet</p>
                        <p class="text-sm">This product hasn't been ordered yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.products.edit', $product)); ?>" 
                       class="block w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition text-center font-semibold">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </a>
                    
                    <a href="<?php echo e(route('admin.products.index')); ?>" 
                       class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Products
                    </a>
                </div>
            </div>

            <!-- Product Statistics -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Orders:</span>
                        <span class="text-lg font-bold text-gray-900"><?php echo e($totalOrders ?? 0); ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Revenue:</span>
                        <span class="text-lg font-bold text-green-600">₵<?php echo e(number_format($totalRevenue ?? 0, 2)); ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">This Month:</span>
                        <span class="text-lg font-bold text-blue-600"><?php echo e($monthlyOrders ?? 0); ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Avg. Order Value:</span>
                        <span class="text-lg font-bold text-purple-600">₵<?php echo e(number_format($avgOrderValue ?? 0, 2)); ?></span>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-red-200">
                <h3 class="text-lg font-semibold text-red-900 mb-4">Danger Zone</h3>
                
                <form method="POST" action="<?php echo e(route('admin.products.destroy', $product)); ?>" 
                      onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" 
                            class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition">
                        <i class="fas fa-trash mr-2"></i>Delete Product
                    </button>
                </form>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $attributes = $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $component = $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/admin/products/show.blade.php ENDPATH**/ ?>