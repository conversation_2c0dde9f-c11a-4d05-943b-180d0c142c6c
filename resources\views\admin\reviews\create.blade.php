<x-layouts.admin title="Add New Review">
    <x-slot name="pageTitle">Add New Review</x-slot>
    <x-slot name="pageDescription">Create a new customer review for the homepage</x-slot>

    <form method="POST" action="{{ route('admin.reviews.store') }}">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Review Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Customer Information -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Customer Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">Customer Name *</label>
                            <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name') }}" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="Enter customer name">
                            @error('customer_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="avatar_initials" class="block text-sm font-medium text-gray-700 mb-2">Avatar Initials *</label>
                            <input type="text" id="avatar_initials" name="avatar_initials" value="{{ old('avatar_initials') }}" 
                                   maxlength="5" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="e.g., JD">
                            <p class="mt-1 text-sm text-gray-500">Maximum 5 characters (e.g., "JD" for John Doe)</p>
                            @error('avatar_initials')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Review Content -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Review Content</h3>
                    
                    <div class="space-y-6">
                        <div>
                            <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">Rating *</label>
                            <select id="rating" name="rating" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="">Select Rating</option>
                                <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>⭐⭐⭐⭐⭐ (5 stars)</option>
                                <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>⭐⭐⭐⭐ (4 stars)</option>
                                <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>⭐⭐⭐ (3 stars)</option>
                                <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>⭐⭐ (2 stars)</option>
                                <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>⭐ (1 star)</option>
                            </select>
                            @error('rating')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="review_text" class="block text-sm font-medium text-gray-700 mb-2">Review Text *</label>
                            <textarea id="review_text" name="review_text" rows="6" required
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="Enter the customer's review...">{{ old('review_text') }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">Maximum 1000 characters</p>
                            @error('review_text')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Review Status -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Review Status</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active (visible on homepage)
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_featured" name="is_featured" value="1"
                                   {{ old('is_featured') ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                Featured review
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                    
                    <div id="review-preview" class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-4">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg" 
                                 alt="Google" class="h-6">
                            <div class="text-yellow-400" id="preview-stars">
                                <!-- Stars will be populated by JavaScript -->
                            </div>
                        </div>
                        <p class="text-gray-600 mb-4 text-sm leading-relaxed" id="preview-text">
                            "Enter review text to see preview..."
                        </p>
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-pink-500 flex items-center justify-center font-bold text-sm mr-3 text-white"
                                 id="preview-avatar">
                                --
                            </div>
                            <span class="font-semibold text-gray-800" id="preview-name">Customer Name</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            <i class="fas fa-save mr-2"></i>Create Review
                        </button>
                        
                        <a href="{{ route('admin.reviews.index') }}" 
                           class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</x-layouts.admin>

@push('scripts')
<script>
// Live preview functionality
function updatePreview() {
    const name = document.getElementById('customer_name').value || 'Customer Name';
    const initials = document.getElementById('avatar_initials').value || '--';
    const text = document.getElementById('review_text').value || 'Enter review text to see preview...';
    const rating = document.getElementById('rating').value || 0;
    
    // Update preview elements
    document.getElementById('preview-name').textContent = name;
    document.getElementById('preview-avatar').textContent = initials.toUpperCase();
    document.getElementById('preview-text').textContent = `"${text}"`;
    
    // Update stars
    const starsContainer = document.getElementById('preview-stars');
    starsContainer.innerHTML = '';
    for (let i = 1; i <= 5; i++) {
        const star = document.createElement('i');
        star.className = i <= rating ? 'fas fa-star' : 'far fa-star';
        starsContainer.appendChild(star);
    }
}

// Add event listeners
document.getElementById('customer_name').addEventListener('input', updatePreview);
document.getElementById('avatar_initials').addEventListener('input', updatePreview);
document.getElementById('review_text').addEventListener('input', updatePreview);
document.getElementById('rating').addEventListener('change', updatePreview);

// Initialize preview
updatePreview();
</script>
@endpush
