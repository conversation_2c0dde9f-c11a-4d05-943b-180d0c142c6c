<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Admin Dashboard - PrintOnline Ghana' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <style>
        .font-heading {
            font-family: 'Poppins', sans-serif;
        }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-100 text-gray-800">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="flex items-center">
                        <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" 
                             alt="PrintOnline Ghana Logo" class="h-8 mr-3">
                        <span class="text-xl font-bold text-gray-900">Admin Dashboard</span>
                    </a>
                </div>

                <!-- Admin Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="{{ route('admin.dashboard') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('admin.dashboard') ? 'text-pink-500' : '' }}">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="{{ route('admin.orders.index') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('admin.orders.*') ? 'text-pink-500' : '' }}">
                        <i class="fas fa-shopping-bag mr-2"></i>Orders
                    </a>
                    <a href="{{ route('admin.products.index') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('admin.products.*') ? 'text-pink-500' : '' }}">
                        <i class="fas fa-box mr-2"></i>Products
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('admin.users.*') ? 'text-pink-500' : '' }}">
                        <i class="fas fa-users mr-2"></i>Users
                    </a>
                </nav>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <a href="{{ route('home') }}" class="text-gray-600 hover:text-pink-500">
                        <i class="fas fa-external-link-alt mr-1"></i>View Site
                    </a>
                    <div class="relative group">
                        <div class="flex items-center gap-2 cursor-pointer">
                            <i class="fas fa-user-circle text-gray-600 text-xl"></i>
                            <span class="text-sm font-semibold text-gray-700">{{ Auth::user()->name }}</span>
                            <i class="fas fa-chevron-down text-gray-500 text-xs"></i>
                        </div>
                        
                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        @if(isset($pageTitle))
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">{{ $pageTitle }}</h1>
                @if(isset($pageDescription))
                    <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
                @endif
            </div>
        @endif

        <!-- Page Content -->
        {{ $slot }}
    </main>

    @stack('scripts')

    <script>
        console.log('PrintOnline Ghana - Admin Dashboard loaded');
    </script>
</body>
</html>
