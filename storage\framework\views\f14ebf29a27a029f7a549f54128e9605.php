<?php if (isset($component)) { $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.admin','data' => ['title' => 'User Details']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.admin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'User Details']); ?>
     <?php $__env->slot('pageTitle', null, []); ?> <?php echo e($user->name); ?> <?php $__env->endSlot(); ?>
     <?php $__env->slot('pageDescription', null, []); ?> View user profile and order history <?php $__env->endSlot(); ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Profile Card -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="text-center">
                    <div class="w-20 h-20 rounded-full flex items-center justify-center font-bold text-xl mx-auto mb-4"
                         style="background-color: <?php echo e($user->avatar_color ?? '#E5E7EB'); ?>; color: <?php echo e($user->avatar_text_color ?? '#374151'); ?>">
                        <?php echo e($user->avatar_initials ?? strtoupper(substr($user->name, 0, 2))); ?>

                    </div>
                    
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($user->name); ?></h3>
                    
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                        <?php echo e($user->role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'); ?>">
                        <?php echo e(ucfirst($user->role)); ?>

                    </span>
                </div>
                
                <div class="mt-6 space-y-3">
                    <div class="flex items-center text-sm">
                        <i class="fas fa-envelope text-gray-400 w-5"></i>
                        <span class="ml-3 text-gray-600"><?php echo e($user->email); ?></span>
                    </div>
                    
                    <?php if($user->phone): ?>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-phone text-gray-400 w-5"></i>
                            <span class="ml-3 text-gray-600"><?php echo e($user->phone); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($user->address): ?>
                        <div class="flex items-start text-sm">
                            <i class="fas fa-map-marker-alt text-gray-400 w-5 mt-0.5"></i>
                            <span class="ml-3 text-gray-600"><?php echo e($user->address); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="flex items-center text-sm">
                        <i class="fas fa-calendar text-gray-400 w-5"></i>
                        <span class="ml-3 text-gray-600">Joined <?php echo e($user->created_at->format('M j, Y')); ?></span>
                    </div>
                    
                    <?php if($user->email_verified_at): ?>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-check-circle text-green-500 w-5"></i>
                            <span class="ml-3 text-green-600">Email Verified</span>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-exclamation-circle text-yellow-500 w-5"></i>
                            <span class="ml-3 text-yellow-600">Email Not Verified</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- User Statistics -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Orders:</span>
                        <span class="text-lg font-bold text-gray-900"><?php echo e($user->orders->count()); ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Spent:</span>
                        <span class="text-lg font-bold text-green-600">
                            ₵<?php echo e(number_format($user->orders->where('status', '!=', 'cancelled')->sum('total_amount'), 2)); ?>

                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Completed Orders:</span>
                        <span class="text-lg font-bold text-blue-600"><?php echo e($user->orders->where('status', 'completed')->count()); ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Pending Orders:</span>
                        <span class="text-lg font-bold text-yellow-600"><?php echo e($user->orders->whereIn('status', ['pending', 'confirmed', 'printing'])->count()); ?></span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <?php if($user->id !== auth()->id()): ?>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="mailto:<?php echo e($user->email); ?>" 
                           class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                            <i class="fas fa-envelope mr-2"></i>Send Email
                        </a>
                        
                        <button onclick="toggleUserRole(<?php echo e($user->id); ?>, '<?php echo e($user->role); ?>')"
                                class="block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition text-center">
                            <i class="fas fa-user-cog mr-2"></i>
                            <?php echo e($user->role === 'admin' ? 'Remove Admin' : 'Make Admin'); ?>

                        </button>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Order History -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Order History</h3>
                    <span class="text-sm text-gray-500"><?php echo e($user->orders->count()); ?> orders total</span>
                </div>
                
                <?php if($user->orders->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $user->orders->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition">
                                <div class="flex items-center justify-between mb-3">
                                    <div>
                                        <h4 class="font-semibold text-gray-900">#<?php echo e($order->order_number); ?></h4>
                                        <p class="text-sm text-gray-500"><?php echo e($order->created_at->format('M j, Y g:i A')); ?></p>
                                    </div>
                                    
                                    <div class="text-right">
                                        <p class="font-semibold text-gray-900"><?php echo e($order->formatted_total); ?></p>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            <?php if($order->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                            <?php elseif($order->status === 'confirmed'): ?> bg-blue-100 text-blue-800
                                            <?php elseif($order->status === 'printing'): ?> bg-purple-100 text-purple-800
                                            <?php elseif($order->status === 'completed'): ?> bg-green-100 text-green-800
                                            <?php elseif($order->status === 'cancelled'): ?> bg-red-100 text-red-800
                                            <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                            <?php echo e(ucfirst($order->status)); ?>

                                        </span>
                                    </div>
                                </div>
                                
                                <?php if($order->items->count() > 0): ?>
                                    <div class="text-sm text-gray-600 mb-3">
                                        <strong>Items:</strong>
                                        <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php echo e($item->product_name); ?> (<?php echo e($item->quantity); ?>x)<?php echo e(!$loop->last ? ', ' : ''); ?>

                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($order->notes): ?>
                                    <div class="text-sm text-gray-600 mb-3">
                                        <strong>Notes:</strong> <?php echo e($order->notes); ?>

                                    </div>
                                <?php endif; ?>
                                
                                <div class="flex justify-end">
                                    <a href="<?php echo e(route('admin.orders.show', $order)); ?>" 
                                       class="text-pink-600 hover:text-pink-800 text-sm font-medium">
                                        View Details <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <i class="fas fa-shopping-bag text-4xl text-gray-300 mb-4"></i>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">No Orders Yet</h4>
                        <p class="text-gray-600">This user hasn't placed any orders yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $attributes = $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $component = $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
function toggleUserRole(userId, currentRole) {
    const newRole = currentRole === 'admin' ? 'customer' : 'admin';
    const action = currentRole === 'admin' ? 'remove admin privileges from' : 'grant admin privileges to';
    
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/users/${userId}/toggle-role`;
        
        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // Add method override
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PATCH';
        form.appendChild(methodInput);
        
        // Add role input
        const roleInput = document.createElement('input');
        roleInput.type = 'hidden';
        roleInput.name = 'role';
        roleInput.value = newRole;
        form.appendChild(roleInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/admin/users/show.blade.php ENDPATH**/ ?>