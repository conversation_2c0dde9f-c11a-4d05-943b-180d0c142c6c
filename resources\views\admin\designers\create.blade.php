<x-layouts.admin title="Add New Designer">
    <x-slot name="pageTitle">Add New Designer</x-slot>
    <x-slot name="pageDescription">Create a new expert designer profile</x-slot>

    <form method="POST" action="{{ route('admin.designers.store') }}" enctype="multipart/form-data">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Designer Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                    
                    <div class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Designer Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="Enter designer name">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Bio *</label>
                            <textarea id="bio" name="bio" rows="4" required
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="Enter designer bio and specialties...">{{ old('bio') }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">Maximum 500 characters</p>
                            @error('bio')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="portfolio_url" class="block text-sm font-medium text-gray-700 mb-2">Portfolio URL</label>
                            <input type="url" id="portfolio_url" name="portfolio_url" value="{{ old('portfolio_url') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://example.com/portfolio">
                            @error('portfolio_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Designer Image -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Designer Image</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
                            <input type="file" id="image" name="image" accept="image/*"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <p class="mt-1 text-sm text-gray-500">Supported formats: JPG, PNG, GIF. Max size: 2MB. Recommended: 400x400px</p>
                            @error('image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div id="image-preview" class="hidden">
                            <img id="preview-img" src="" alt="Preview" class="w-32 h-32 object-cover rounded-lg border">
                        </div>
                    </div>
                </div>

                <!-- Social Links -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Social Links</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="behance" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-behance mr-2 text-blue-600"></i>Behance
                            </label>
                            <input type="url" id="behance" name="social_links[behance]" value="{{ old('social_links.behance') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://behance.net/username">
                            @error('social_links.behance')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="dribbble" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-dribbble mr-2 text-pink-600"></i>Dribbble
                            </label>
                            <input type="url" id="dribbble" name="social_links[dribbble]" value="{{ old('social_links.dribbble') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://dribbble.com/username">
                            @error('social_links.dribbble')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="instagram" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-instagram mr-2 text-purple-600"></i>Instagram
                            </label>
                            <input type="url" id="instagram" name="social_links[instagram]" value="{{ old('social_links.instagram') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://instagram.com/username">
                            @error('social_links.instagram')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="linkedin" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-linkedin mr-2 text-blue-700"></i>LinkedIn
                            </label>
                            <input type="url" id="linkedin" name="social_links[linkedin]" value="{{ old('social_links.linkedin') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://linkedin.com/in/username">
                            @error('social_links.linkedin')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Designer Status -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Designer Status</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active (visible on designers page)
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Display Settings</h3>
                    
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                        <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                               min="0"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                               placeholder="0">
                        <p class="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
                    </div>
                </div>

                <!-- Preview -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                    
                    <div id="designer-preview" class="text-center p-4 border border-gray-200 rounded-lg">
                        <div class="w-24 h-24 rounded-full mx-auto mb-4 bg-gray-200 flex items-center justify-center" id="preview-avatar">
                            <i class="fas fa-user text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2" id="preview-name">Designer Name</h3>
                        <p class="text-gray-500 text-sm leading-relaxed" id="preview-bio">Enter bio to see preview...</p>
                        <div class="flex justify-center space-x-3 mt-4" id="preview-social">
                            <!-- Social links will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            <i class="fas fa-save mr-2"></i>Create Designer
                        </button>
                        
                        <a href="{{ route('admin.designers.index') }}" 
                           class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</x-layouts.admin>

@push('scripts')
<script>
// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-img').src = e.target.result;
            document.getElementById('image-preview').classList.remove('hidden');
            
            // Update preview avatar
            const previewAvatar = document.getElementById('preview-avatar');
            previewAvatar.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-cover rounded-full">`;
        };
        reader.readAsDataURL(file);
    }
});

// Live preview functionality
function updatePreview() {
    const name = document.getElementById('name').value || 'Designer Name';
    const bio = document.getElementById('bio').value || 'Enter bio to see preview...';
    
    document.getElementById('preview-name').textContent = name;
    document.getElementById('preview-bio').textContent = bio;
    
    // Update social links
    const socialContainer = document.getElementById('preview-social');
    socialContainer.innerHTML = '';
    
    const socialLinks = {
        behance: { icon: 'fab fa-behance', color: 'text-blue-600' },
        dribbble: { icon: 'fab fa-dribbble', color: 'text-pink-600' },
        instagram: { icon: 'fab fa-instagram', color: 'text-purple-600' },
        linkedin: { icon: 'fab fa-linkedin', color: 'text-blue-700' }
    };
    
    Object.keys(socialLinks).forEach(platform => {
        const input = document.querySelector(`input[name="social_links[${platform}]"]`);
        if (input && input.value) {
            const link = document.createElement('a');
            link.href = '#';
            link.className = `${socialLinks[platform].color} hover:opacity-75 transition`;
            link.innerHTML = `<i class="${socialLinks[platform].icon} text-xl"></i>`;
            socialContainer.appendChild(link);
        }
    });
}

// Add event listeners
document.getElementById('name').addEventListener('input', updatePreview);
document.getElementById('bio').addEventListener('input', updatePreview);
document.querySelectorAll('input[name^="social_links"]').forEach(input => {
    input.addEventListener('input', updatePreview);
});

// Initialize preview
updatePreview();
</script>
@endpush
