<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\Request;

class ReviewController extends Controller
{
    /**
     * Display a listing of reviews
     */
    public function index()
    {
        $reviews = Review::orderBy('created_at', 'desc')->paginate(20);
        
        return view('admin.reviews.index', compact('reviews'));
    }

    /**
     * Show the form for creating a new review
     */
    public function create()
    {
        return view('admin.reviews.create');
    }

    /**
     * Store a newly created review
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'avatar_initials' => 'required|string|max:5',
            'review_text' => 'required|string|max:1000',
            'rating' => 'required|integer|min:1|max:5',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        Review::create([
            'customer_name' => $request->customer_name,
            'avatar_initials' => strtoupper($request->avatar_initials),
            'review_text' => $request->review_text,
            'rating' => $request->rating,
            'is_featured' => $request->boolean('is_featured'),
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()
            ->route('admin.reviews.index')
            ->with('success', 'Review created successfully.');
    }

    /**
     * Display the specified review
     */
    public function show(Review $review)
    {
        return view('admin.reviews.show', compact('review'));
    }

    /**
     * Show the form for editing the specified review
     */
    public function edit(Review $review)
    {
        return view('admin.reviews.edit', compact('review'));
    }

    /**
     * Update the specified review
     */
    public function update(Request $request, Review $review)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'avatar_initials' => 'required|string|max:5',
            'review_text' => 'required|string|max:1000',
            'rating' => 'required|integer|min:1|max:5',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $review->update([
            'customer_name' => $request->customer_name,
            'avatar_initials' => strtoupper($request->avatar_initials),
            'review_text' => $request->review_text,
            'rating' => $request->rating,
            'is_featured' => $request->boolean('is_featured'),
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()
            ->route('admin.reviews.index')
            ->with('success', 'Review updated successfully.');
    }

    /**
     * Remove the specified review
     */
    public function destroy(Review $review)
    {
        $review->delete();

        return redirect()
            ->route('admin.reviews.index')
            ->with('success', 'Review deleted successfully.');
    }
}
