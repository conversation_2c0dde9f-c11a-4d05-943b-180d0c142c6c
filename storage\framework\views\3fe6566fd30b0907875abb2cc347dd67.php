<?php if (isset($component)) { $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.admin','data' => ['title' => 'Dashboard']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.admin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Dashboard']); ?>
     <?php $__env->slot('pageTitle', null, []); ?> Dashboard <?php $__env->endSlot(); ?>
     <?php $__env->slot('pageDescription', null, []); ?> Overview of your print shop performance <?php $__env->endSlot(); ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">Total Revenue</h3>
                    <p class="text-3xl font-bold mt-2">₵<?php echo e(number_format($stats['total_revenue'], 2)); ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- New Orders -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">New Orders (30 days)</h3>
                    <p class="text-3xl font-bold mt-2"><?php echo e($stats['new_orders']); ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- New Users -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">New Users (30 days)</h3>
                    <p class="text-3xl font-bold mt-2"><?php echo e($stats['new_users']); ?></p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Products -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">Active Products</h3>
                    <p class="text-3xl font-bold mt-2"><?php echo e($stats['total_products']); ?></p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-box text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Sales Chart -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <h3 class="text-lg font-bold mb-4">Sales Overview (Last 7 Days)</h3>
            <canvas id="salesChart" width="400" height="200"></canvas>
        </div>

        <!-- Recent Orders -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold">Recent Orders</h3>
                <a href="<?php echo e(route('admin.orders.index')); ?>" 
                   class="text-pink-500 hover:text-pink-600 text-sm font-semibold">
                    View All
                </a>
            </div>
            
            <?php if($recentOrders->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $recentOrders->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <div>
                                <p class="font-semibold text-sm"><?php echo e($order->order_number); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($order->user->name); ?></p>
                                <p class="text-xs text-gray-400"><?php echo e($order->created_at->format('M j, Y g:i A')); ?></p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-sm"><?php echo e($order->formatted_total); ?></p>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full
                                    <?php if($order->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($order->status === 'confirmed'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($order->status === 'printing'): ?> bg-purple-100 text-purple-800
                                    <?php elseif($order->status === 'completed'): ?> bg-green-100 text-green-800
                                    <?php else: ?> bg-red-100 text-red-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($order->status)); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-shopping-cart text-3xl mb-2"></i>
                    <p>No orders yet</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="<?php echo e(route('admin.orders.index')); ?>" 
           class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Manage Orders</h4>
                    <p class="text-sm text-gray-500">View and update order status</p>
                </div>
            </div>
        </a>

        <a href="<?php echo e(route('admin.products.index')); ?>" 
           class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-box text-green-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Manage Products</h4>
                    <p class="text-sm text-gray-500">Add and edit products</p>
                </div>
            </div>
        </a>

        <a href="<?php echo e(route('admin.settings.index')); ?>" 
           class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cog text-purple-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Settings</h4>
                    <p class="text-sm text-gray-500">Configure site settings</p>
                </div>
            </div>
        </a>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($chartLabels, 15, 512) ?>,
                datasets: [{
                    label: 'Sales (₵)',
                    data: <?php echo json_encode($chartData, 15, 512) ?>,
                    backgroundColor: 'rgba(251, 113, 133, 0.1)',
                    borderColor: '#fb7185',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₵' + value;
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $attributes = $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $component = $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>