<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Designer;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class DesignerController extends Controller
{
    /**
     * Display a listing of designers
     */
    public function index()
    {
        $designers = Designer::orderBy('sort_order')->orderBy('name')->paginate(20);
        
        return view('admin.designers.index', compact('designers'));
    }

    /**
     * Show the form for creating a new designer
     */
    public function create()
    {
        return view('admin.designers.create');
    }

    /**
     * Store a newly created designer
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'bio' => 'required|string|max:500',
            'portfolio_url' => 'nullable|url|max:255',
            'social_links.behance' => 'nullable|url|max:255',
            'social_links.dribbble' => 'nullable|url|max:255',
            'social_links.instagram' => 'nullable|url|max:255',
            'social_links.linkedin' => 'nullable|url|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $designer = Designer::create([
            'name' => $request->name,
            'bio' => $request->bio,
            'portfolio_url' => $request->portfolio_url,
            'social_links' => array_filter([
                'behance' => $request->input('social_links.behance'),
                'dribbble' => $request->input('social_links.dribbble'),
                'instagram' => $request->input('social_links.instagram'),
                'linkedin' => $request->input('social_links.linkedin'),
            ]),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->integer('sort_order', 0),
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $designer->addMediaFromRequest('image')
                ->toMediaCollection('images');
        }

        return redirect()
            ->route('admin.designers.show', $designer)
            ->with('success', 'Designer created successfully.');
    }

    /**
     * Display the specified designer
     */
    public function show(Designer $designer)
    {
        return view('admin.designers.show', compact('designer'));
    }

    /**
     * Show the form for editing the specified designer
     */
    public function edit(Designer $designer)
    {
        return view('admin.designers.edit', compact('designer'));
    }

    /**
     * Update the specified designer
     */
    public function update(Request $request, Designer $designer)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'bio' => 'required|string|max:500',
            'portfolio_url' => 'nullable|url|max:255',
            'social_links.behance' => 'nullable|url|max:255',
            'social_links.dribbble' => 'nullable|url|max:255',
            'social_links.instagram' => 'nullable|url|max:255',
            'social_links.linkedin' => 'nullable|url|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $designer->update([
            'name' => $request->name,
            'bio' => $request->bio,
            'portfolio_url' => $request->portfolio_url,
            'social_links' => array_filter([
                'behance' => $request->input('social_links.behance'),
                'dribbble' => $request->input('social_links.dribbble'),
                'instagram' => $request->input('social_links.instagram'),
                'linkedin' => $request->input('social_links.linkedin'),
            ]),
            'is_active' => $request->boolean('is_active'),
            'sort_order' => $request->integer('sort_order', 0),
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $designer->clearMediaCollection('images');
            $designer->addMediaFromRequest('image')
                ->toMediaCollection('images');
        }

        return redirect()
            ->route('admin.designers.show', $designer)
            ->with('success', 'Designer updated successfully.');
    }

    /**
     * Remove the specified designer
     */
    public function destroy(Designer $designer)
    {
        $designer->delete();

        return redirect()
            ->route('admin.designers.index')
            ->with('success', 'Designer deleted successfully.');
    }
}
