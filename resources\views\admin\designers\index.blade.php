<x-layouts.admin title="Designers Management">
    <x-slot name="pageTitle">Designers Management</x-slot>
    <x-slot name="pageDescription">Manage expert designers displayed on the designers page</x-slot>

    <div class="mb-6 flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <div class="bg-white px-4 py-2 rounded-lg shadow-sm">
                <span class="text-sm text-gray-600">Total Designers: </span>
                <span class="font-semibold">{{ $designers->total() }}</span>
            </div>
            <div class="bg-white px-4 py-2 rounded-lg shadow-sm">
                <span class="text-sm text-gray-600">Active: </span>
                <span class="font-semibold text-green-600">{{ $designers->where('is_active', true)->count() }}</span>
            </div>
        </div>
        
        <a href="{{ route('admin.designers.create') }}" 
           class="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
            <i class="fas fa-plus mr-2"></i>Add Designer
        </a>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        @if($designers->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bio</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portfolio</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort Order</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($designers as $designer)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <img src="{{ $designer->image_url }}" 
                                             alt="{{ $designer->name }}" 
                                             class="w-12 h-12 rounded-full object-cover mr-4">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $designer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $designer->initials }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate">{{ $designer->bio }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($designer->portfolio_url)
                                        <a href="{{ $designer->portfolio_url }}" 
                                           target="_blank"
                                           class="text-pink-600 hover:text-pink-900 text-sm">
                                            <i class="fas fa-external-link-alt mr-1"></i>View Portfolio
                                        </a>
                                    @else
                                        <span class="text-gray-400 text-sm">No portfolio</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        {{ $designer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $designer->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $designer->sort_order }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ route('admin.designers.show', $designer) }}" 
                                           class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.designers.edit', $designer) }}" 
                                           class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ route('admin.designers.destroy', $designer) }}" 
                                              class="inline" onsubmit="return confirm('Are you sure you want to delete this designer?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $designers->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-palette text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No designers yet</h3>
                <p class="text-gray-500 mb-6">Start by adding your first expert designer.</p>
                <a href="{{ route('admin.designers.create') }}" 
                   class="inline-flex items-center px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition">
                    <i class="fas fa-plus mr-2"></i>Add Designer
                </a>
            </div>
        @endif
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-sort-numeric-down text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Reorder Designers</h4>
                    <p class="text-sm text-gray-500">Change the display order on the designers page</p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-eye text-green-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">View Designers Page</h4>
                    <p class="text-sm text-gray-500">See how designers appear to visitors</p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Designer Analytics</h4>
                    <p class="text-sm text-gray-500">Track designer page performance</p>
                </div>
            </div>
        </div>
    </div>
</x-layouts.admin>
