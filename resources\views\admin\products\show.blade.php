<x-layouts.admin title="Product Details">
    <x-slot name="pageTitle">{{ $product->name }}</x-slot>
    <x-slot name="pageDescription">View product details and manage settings</x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Product Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Product Overview -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $product->name }}</h3>
                        <div class="flex items-center gap-4">
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                                {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            
                            @if($product->is_featured)
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            @endif
                            
                            <span class="text-sm text-gray-500">{{ $product->category }}</span>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <p class="text-3xl font-bold text-pink-600">₵{{ number_format($product->base_price, 2) }}</p>
                        <p class="text-sm text-gray-500">Base Price</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Product Image -->
                    <div>
                        @if($product->image_url)
                            <img src="{{ $product->image_url }}" alt="{{ $product->name }}" 
                                 class="w-full h-64 object-cover rounded-lg border">
                        @else
                            <div class="w-full h-64 bg-gray-100 rounded-lg border flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Product Details -->
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">{{ $product->description ?: 'No description provided.' }}</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">Product Information</h4>
                            <dl class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">SKU:</dt>
                                    <dd class="text-gray-900">{{ $product->sku ?? 'N/A' }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Category:</dt>
                                    <dd class="text-gray-900">{{ $product->category }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Sort Order:</dt>
                                    <dd class="text-gray-900">{{ $product->sort_order }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">URL Slug:</dt>
                                    <dd class="text-gray-900">{{ $product->slug }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Created:</dt>
                                    <dd class="text-gray-900">{{ $product->created_at->format('M j, Y') }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-gray-500">Last Updated:</dt>
                                    <dd class="text-gray-900">{{ $product->updated_at->format('M j, Y') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Recent Orders</h3>
                
                @if(isset($recentOrders) && $recentOrders->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentOrders as $order)
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div>
                                    <p class="font-semibold text-gray-900">#{{ $order->order_number }}</p>
                                    <p class="text-sm text-gray-600">{{ $order->user->name }}</p>
                                    <p class="text-sm text-gray-500">{{ $order->created_at->format('M j, Y') }}</p>
                                </div>
                                
                                <div class="text-right">
                                    <p class="font-semibold text-gray-900">₵{{ number_format($order->total_amount, 2) }}</p>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                                        @elseif($order->status === 'printing') bg-purple-100 text-purple-800
                                        @elseif($order->status === 'completed') bg-green-100 text-green-800
                                        @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-shopping-bag text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg">No Orders Yet</p>
                        <p class="text-sm">This product hasn't been ordered yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.products.edit', $product) }}" 
                       class="block w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition text-center font-semibold">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </a>
                    
                    <a href="{{ route('admin.products.index') }}" 
                       class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Products
                    </a>
                </div>
            </div>

            <!-- Product Statistics -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Orders:</span>
                        <span class="text-lg font-bold text-gray-900">{{ $totalOrders ?? 0 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Total Revenue:</span>
                        <span class="text-lg font-bold text-green-600">₵{{ number_format($totalRevenue ?? 0, 2) }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">This Month:</span>
                        <span class="text-lg font-bold text-blue-600">{{ $monthlyOrders ?? 0 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Avg. Order Value:</span>
                        <span class="text-lg font-bold text-purple-600">₵{{ number_format($avgOrderValue ?? 0, 2) }}</span>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="bg-white rounded-xl shadow-md p-6 border border-red-200">
                <h3 class="text-lg font-semibold text-red-900 mb-4">Danger Zone</h3>
                
                <form method="POST" action="{{ route('admin.products.destroy', $product) }}" 
                      onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition">
                        <i class="fas fa-trash mr-2"></i>Delete Product
                    </button>
                </form>
            </div>
        </div>
    </div>
</x-layouts.admin>
