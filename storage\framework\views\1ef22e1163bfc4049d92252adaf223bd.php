<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['product']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['product']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="product-card">
    <!-- Product Image -->
    <div class="product-image-wrapper relative">
        <i class="fas fa-star brand-icon absolute top-4 left-4 text-xl text-gray-800"></i>
        <img src="<?php echo e($product->image_url); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-auto rounded-lg">
    </div>

    <!-- Card Body -->
    <div class="card-body flex-grow flex flex-col">
        <!-- Tags -->
        <div class="card-tags flex justify-between items-center mb-3 min-h-[28px]">
            <?php if($product->is_best_seller): ?>
                <div class="best-seller-tag-v2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold">
                    Best Seller
                </div>
            <?php else: ?>
                <span></span>
            <?php endif; ?>
        </div>

        <!-- Product Name -->
        <h3 class="font-bold text-lg mt-1 mb-4"><?php echo e($product->name); ?></h3>

        <!-- Card Footer -->
        <div class="card-footer flex justify-between items-center mt-auto">
            <div>
                <p class="text-sm text-gray-500">From</p>
                <p class="font-bold text-gray-800 text-lg">₵<?php echo e(number_format($product->starting_price, 2)); ?></p>
            </div>
            <a href="<?php echo e(route('product.show', $product)); ?>" 
               class="bg-gray-900 text-white font-bold py-3 px-6 rounded-xl hover:bg-gray-800 transition">
                Print
            </a>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/components/product-card.blade.php ENDPATH**/ ?>