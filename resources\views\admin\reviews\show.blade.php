<x-layouts.admin title="Review Details">
    <x-slot name="pageTitle">Review: {{ $review->customer_name }}</x-slot>
    <x-slot name="pageDescription">View customer review details</x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Review Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Review Content -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Review Content</h3>
                
                <div class="space-y-6">
                    <!-- Customer Info -->
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center font-bold text-lg mr-4"
                             style="background-color: {{ $review->avatar_color }}; color: {{ $review->avatar_text_color }}">
                            {{ $review->avatar_initials }}
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900">{{ $review->customer_name }}</h4>
                            <div class="flex items-center mt-1">
                                <div class="text-yellow-400 mr-2">
                                    {!! $review->star_rating_html !!}
                                </div>
                                <span class="text-sm text-gray-600">({{ $review->rating }} out of 5 stars)</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Review Text -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <p class="text-gray-800 leading-relaxed text-lg">
                            "{{ $review->review_text }}"
                        </p>
                    </div>
                    
                    <!-- Review Meta -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-gray-200">
                        <div>
                            <h5 class="font-medium text-gray-900 mb-2">Status</h5>
                            <div class="flex flex-wrap gap-2">
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                                    {{ $review->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $review->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                @if($review->is_featured)
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-pink-100 text-pink-800">
                                        Featured
                                    </span>
                                @endif
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-medium text-gray-900 mb-2">Created</h5>
                            <p class="text-gray-600">{{ $review->created_at->format('F j, Y \a\t g:i A') }}</p>
                            @if($review->updated_at != $review->created_at)
                                <p class="text-sm text-gray-500 mt-1">
                                    Last updated: {{ $review->updated_at->format('F j, Y \a\t g:i A') }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Homepage Preview -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Homepage Preview</h3>
                <p class="text-gray-600 mb-4">This is how the review appears on the homepage carousel:</p>
                
                <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
                    <div class="flex items-start justify-between mb-4">
                        <img src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg" 
                             alt="Google" class="h-6">
                        <div class="text-yellow-400">
                            {!! $review->star_rating_html !!}
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4 text-sm leading-relaxed">"{{ $review->review_text }}"</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm mr-3"
                             style="background-color: {{ $review->avatar_color }}; color: {{ $review->avatar_text_color }}">
                            {{ $review->avatar_initials }}
                        </div>
                        <span class="font-semibold text-gray-800">{{ $review->customer_name }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Rating:</span>
                        <span class="font-semibold">{{ $review->rating }}/5</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Status:</span>
                        <span class="font-semibold {{ $review->is_active ? 'text-green-600' : 'text-red-600' }}">
                            {{ $review->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Featured:</span>
                        <span class="font-semibold {{ $review->is_featured ? 'text-pink-600' : 'text-gray-600' }}">
                            {{ $review->is_featured ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Characters:</span>
                        <span class="font-semibold">{{ strlen($review->review_text) }}/1000</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.reviews.edit', $review) }}" 
                       class="block w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition text-center">
                        <i class="fas fa-edit mr-2"></i>Edit Review
                    </a>
                    
                    <a href="{{ route('admin.reviews.index') }}" 
                       class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Reviews
                    </a>
                    
                    <form method="POST" action="{{ route('admin.reviews.destroy', $review) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this review? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition">
                            <i class="fas fa-trash mr-2"></i>Delete Review
                        </button>
                    </form>
                </div>
            </div>

            <!-- Related Reviews -->
            @if($relatedReviews = App\Models\Review::where('id', '!=', $review->id)->where('rating', $review->rating)->limit(3)->get())
                @if($relatedReviews->count() > 0)
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Other {{ $review->rating }}-Star Reviews</h3>
                        
                        <div class="space-y-3">
                            @foreach($relatedReviews as $related)
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-medium text-sm">{{ $related->customer_name }}</span>
                                        <div class="text-yellow-400 text-xs">
                                            {!! $related->star_rating_html !!}
                                        </div>
                                    </div>
                                    <p class="text-xs text-gray-600 line-clamp-2">{{ Str::limit($related->review_text, 80) }}</p>
                                    <a href="{{ route('admin.reviews.show', $related) }}" 
                                       class="text-xs text-pink-600 hover:text-pink-800 mt-1 inline-block">
                                        View →
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>
</x-layouts.admin>
