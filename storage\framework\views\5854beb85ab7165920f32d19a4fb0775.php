<?php if (isset($component)) { $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.admin','data' => ['title' => 'Order Details']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.admin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Order Details']); ?>
     <?php $__env->slot('pageTitle', null, []); ?> Order #<?php echo e($order->order_number); ?> <?php $__env->endSlot(); ?>
     <?php $__env->slot('pageDescription', null, []); ?> View and manage order details <?php $__env->endSlot(); ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Order Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Order Items -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Order Items</h3>
                
                <div class="space-y-4">
                    <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center space-x-4">
                                <?php if($item->product && $item->product->image_url): ?>
                                    <img src="<?php echo e($item->product->image_url); ?>" alt="<?php echo e($item->product_name); ?>" 
                                         class="w-16 h-16 object-cover rounded-lg">
                                <?php else: ?>
                                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-image text-gray-400"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div>
                                    <h4 class="font-semibold text-gray-900"><?php echo e($item->product_name); ?></h4>
                                    <p class="text-sm text-gray-600"><?php echo e($item->formatted_options); ?></p>
                                    <p class="text-sm text-gray-500">Quantity: <?php echo e($item->quantity); ?></p>
                                </div>
                            </div>
                            
                            <div class="text-right">
                                <p class="font-semibold text-gray-900"><?php echo e($item->formatted_total_price); ?></p>
                                <p class="text-sm text-gray-500"><?php echo e($item->formatted_unit_price); ?> each</p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Order Total -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                        <span class="text-2xl font-bold text-pink-600"><?php echo e($order->formatted_total); ?></span>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Customer Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Customer Details</h4>
                        <p class="text-gray-600"><?php echo e($order->user->name); ?></p>
                        <p class="text-gray-600"><?php echo e($order->user->email); ?></p>
                        <?php if($order->user->phone): ?>
                            <p class="text-gray-600"><?php echo e($order->user->phone); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <?php if($order->billing_address): ?>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Billing Address</h4>
                            <div class="text-gray-600 text-sm">
                                <p><?php echo e($order->billing_address['name'] ?? ''); ?></p>
                                <p><?php echo e($order->billing_address['address'] ?? ''); ?></p>
                                <p><?php echo e($order->billing_address['city'] ?? ''); ?>, <?php echo e($order->billing_address['region'] ?? ''); ?></p>
                                <?php if(isset($order->billing_address['phone'])): ?>
                                    <p><?php echo e($order->billing_address['phone']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($order->shipping_address && $order->shipping_address !== $order->billing_address): ?>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Shipping Address</h4>
                            <div class="text-gray-600 text-sm">
                                <p><?php echo e($order->shipping_address['name'] ?? ''); ?></p>
                                <p><?php echo e($order->shipping_address['address'] ?? ''); ?></p>
                                <p><?php echo e($order->shipping_address['city'] ?? ''); ?>, <?php echo e($order->shipping_address['region'] ?? ''); ?></p>
                                <?php if(isset($order->shipping_address['phone'])): ?>
                                    <p><?php echo e($order->shipping_address['phone']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Order Notes -->
            <?php if($order->notes): ?>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Notes</h3>
                    <p class="text-gray-600"><?php echo e($order->notes); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Order Status & Actions -->
        <div class="space-y-6">
            <!-- Order Status -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Status</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Current Status:</span>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                            <?php if($order->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                            <?php elseif($order->status === 'confirmed'): ?> bg-blue-100 text-blue-800
                            <?php elseif($order->status === 'printing'): ?> bg-purple-100 text-purple-800
                            <?php elseif($order->status === 'completed'): ?> bg-green-100 text-green-800
                            <?php elseif($order->status === 'cancelled'): ?> bg-red-100 text-red-800
                            <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                            <?php echo e(ucfirst($order->status)); ?>

                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Order Date:</span>
                        <span class="text-sm text-gray-900"><?php echo e($order->created_at->format('M j, Y g:i A')); ?></span>
                    </div>
                    
                    <?php if($order->confirmed_at): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Confirmed:</span>
                            <span class="text-sm text-gray-900"><?php echo e($order->confirmed_at->format('M j, Y g:i A')); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($order->completed_at): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-600">Completed:</span>
                            <span class="text-sm text-gray-900"><?php echo e($order->completed_at->format('M j, Y g:i A')); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Status Update Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Status</h3>
                
                <form method="POST" action="<?php echo e(route('admin.orders.update-status', $order)); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PATCH'); ?>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                            <select name="status" id="status" 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="pending" <?php echo e($order->status === 'pending' ? 'selected' : ''); ?>>Pending</option>
                                <option value="confirmed" <?php echo e($order->status === 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                                <option value="printing" <?php echo e($order->status === 'printing' ? 'selected' : ''); ?>>Printing</option>
                                <option value="completed" <?php echo e($order->status === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                <option value="cancelled" <?php echo e($order->status === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                            </select>
                        </div>
                        
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            Update Status
                        </button>
                    </div>
                </form>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.orders.index')); ?>" 
                       class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Orders
                    </a>
                    
                    <button onclick="window.print()" 
                            class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                        <i class="fas fa-print mr-2"></i>Print Order
                    </button>
                    
                    <a href="mailto:<?php echo e($order->user->email); ?>?subject=Order <?php echo e($order->order_number); ?>" 
                       class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition text-center">
                        <i class="fas fa-envelope mr-2"></i>Email Customer
                    </a>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $attributes = $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $component = $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/admin/orders/show.blade.php ENDPATH**/ ?>