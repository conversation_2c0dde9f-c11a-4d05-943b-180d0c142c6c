<?php if (isset($component)) { $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.admin','data' => ['title' => 'Edit Product']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.admin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Edit Product']); ?>
     <?php $__env->slot('pageTitle', null, []); ?> Edit Product: <?php echo e($product->name); ?> <?php $__env->endSlot(); ?>
     <?php $__env->slot('pageDescription', null, []); ?> Update product information and settings <?php $__env->endSlot(); ?>

    <form method="POST" action="<?php echo e(route('admin.products.update', $product)); ?>" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Product Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                            <input type="text" id="name" name="name" value="<?php echo e(old('name', $product->name)); ?>" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="Enter product name">
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                            <select id="category" name="category" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="">Select Category</option>
                                <option value="Apparel" <?php echo e(old('category', $product->category) === 'Apparel' ? 'selected' : ''); ?>>Apparel</option>
                                <option value="Office" <?php echo e(old('category', $product->category) === 'Office' ? 'selected' : ''); ?>>Office</option>
                                <option value="Giftware" <?php echo e(old('category', $product->category) === 'Giftware' ? 'selected' : ''); ?>>Giftware</option>
                                <option value="Promotional" <?php echo e(old('category', $product->category) === 'Promotional' ? 'selected' : ''); ?>>Promotional</option>
                                <option value="Stationery" <?php echo e(old('category', $product->category) === 'Stationery' ? 'selected' : ''); ?>>Stationery</option>
                                <option value="Signage" <?php echo e(old('category', $product->category) === 'Signage' ? 'selected' : ''); ?>>Signage</option>
                            </select>
                            <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div>
                            <label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">Base Price (₵) *</label>
                            <input type="number" id="base_price" name="base_price" value="<?php echo e(old('base_price', $product->base_price)); ?>" 
                                   step="0.01" min="0" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="0.00">
                            <?php $__errorArgs = ['base_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="description" name="description" rows="4"
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="Enter product description"><?php echo e(old('description', $product->description)); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Product Image -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Image</h3>
                    
                    <div class="space-y-4">
                        <?php if($product->image_url): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Current Image</label>
                                <img src="<?php echo e($product->image_url); ?>" alt="<?php echo e($product->name); ?>" 
                                     class="w-32 h-32 object-cover rounded-lg border">
                            </div>
                        <?php endif; ?>
                        
                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo e($product->image_url ? 'Replace Image' : 'Upload Image'); ?>

                            </label>
                            <input type="file" id="image" name="image" accept="image/*"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <p class="mt-1 text-sm text-gray-500">Supported formats: JPG, PNG, GIF. Max size: 2MB</p>
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        
                        <div id="image-preview" class="hidden">
                            <img id="preview-img" src="" alt="Preview" class="w-32 h-32 object-cover rounded-lg border">
                        </div>
                    </div>
                </div>

                <!-- Product Options -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Options</h3>
                    
                    <div id="options-container">
                        <?php if($product->options && $product->options->count() > 0): ?>
                            <?php $__currentLoopData = $product->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="option-group border border-gray-200 rounded-lg p-4 mb-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="font-medium text-gray-900"><?php echo e($option->name); ?></h4>
                                        <button type="button" onclick="removeOption(this)" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Option Name</label>
                                            <input type="text" name="options[<?php echo e($loop->index); ?>][name]" 
                                                   value="<?php echo e($option->name); ?>"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Required</label>
                                            <select name="options[<?php echo e($loop->index); ?>][required]" 
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                                <option value="1" <?php echo e($option->required ? 'selected' : ''); ?>>Yes</option>
                                                <option value="0" <?php echo e(!$option->required ? 'selected' : ''); ?>>No</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="option-values">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Option Values</label>
                                        <div class="values-container">
                                            <?php $__currentLoopData = $option->values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex items-center gap-2 mb-2">
                                                    <input type="text" name="options[<?php echo e($loop->parent->index); ?>][values][<?php echo e($loop->index); ?>][name]" 
                                                           value="<?php echo e($value->name); ?>"
                                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                                    <input type="number" name="options[<?php echo e($loop->parent->index); ?>][values][<?php echo e($loop->index); ?>][price_modifier]" 
                                                           value="<?php echo e($value->price_modifier); ?>"
                                                           class="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                                           step="0.01">
                                                    <button type="button" onclick="removeOptionValue(this)" class="text-red-600 hover:text-red-800">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <button type="button" onclick="addOptionValue(this, <?php echo e($loop->index); ?>)" 
                                                class="mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition text-sm">
                                            <i class="fas fa-plus mr-1"></i>Add Value
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-plus-circle text-4xl mb-4"></i>
                                <p>Click "Add Option" to create product variations like size, color, material, etc.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <button type="button" onclick="addOption()" 
                            class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                        <i class="fas fa-plus mr-2"></i>Add Option
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Product Status -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Status</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" 
                                   <?php echo e(old('is_active', $product->is_active) ? 'checked' : ''); ?>

                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active (visible to customers)
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_featured" name="is_featured" value="1"
                                   <?php echo e(old('is_featured', $product->is_featured) ? 'checked' : ''); ?>

                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                Featured product
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Display Settings</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                            <input type="number" id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $product->sort_order)); ?>" 
                                   min="0"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <p class="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
                        </div>
                        
                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">URL Slug</label>
                            <input type="text" id="slug" name="slug" value="<?php echo e(old('slug', $product->slug)); ?>"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <p class="mt-1 text-sm text-gray-500">Leave blank to auto-generate</p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            <i class="fas fa-save mr-2"></i>Update Product
                        </button>
                        
                        <a href="<?php echo e(route('admin.products.show', $product)); ?>" 
                           class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                            <i class="fas fa-eye mr-2"></i>View Product
                        </a>
                        
                        <a href="<?php echo e(route('admin.products.index')); ?>" 
                           class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Products
                        </a>
                        
                        <form method="POST" action="<?php echo e(route('admin.products.destroy', $product)); ?>" 
                              onsubmit="return confirm('Are you sure you want to delete this product?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition">
                                <i class="fas fa-trash mr-2"></i>Delete Product
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </form>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $attributes = $__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__attributesOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3)): ?>
<?php $component = $__componentOriginalc8c9fd5d7827a77a31381de67195f0c3; ?>
<?php unset($__componentOriginalc8c9fd5d7827a77a31381de67195f0c3); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
let optionIndex = <?php echo e($product->options ? $product->options->count() : 0); ?>;

// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-img').src = e.target.result;
            document.getElementById('image-preview').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});

// Auto-generate slug from name (only if slug is empty)
document.getElementById('name').addEventListener('input', function(e) {
    const slugField = document.getElementById('slug');
    if (!slugField.value) {
        const slug = e.target.value
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugField.value = slug;
    }
});

function addOption() {
    const container = document.getElementById('options-container');
    
    // Remove empty state if it exists
    if (container.querySelector('.text-center')) {
        container.innerHTML = '';
    }
    
    const optionHtml = `
        <div class="option-group border border-gray-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between mb-4">
                <h4 class="font-medium text-gray-900">Option ${optionIndex + 1}</h4>
                <button type="button" onclick="removeOption(this)" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Option Name</label>
                    <input type="text" name="options[${optionIndex}][name]" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                           placeholder="e.g., Size, Color, Material">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Required</label>
                    <select name="options[${optionIndex}][required]" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </div>
            </div>
            
            <div class="option-values">
                <label class="block text-sm font-medium text-gray-700 mb-2">Option Values</label>
                <div class="values-container"></div>
                <button type="button" onclick="addOptionValue(this, ${optionIndex})" 
                        class="mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition text-sm">
                    <i class="fas fa-plus mr-1"></i>Add Value
                </button>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', optionHtml);
    optionIndex++;
}

function removeOption(button) {
    button.closest('.option-group').remove();
    
    // Show empty state if no options left
    const container = document.getElementById('options-container');
    if (container.children.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-plus-circle text-4xl mb-4"></i>
                <p>Click "Add Option" to create product variations like size, color, material, etc.</p>
            </div>
        `;
    }
}

function addOptionValue(button, optionIndex) {
    const container = button.previousElementSibling;
    const valueIndex = container.children.length;
    
    const valueHtml = `
        <div class="flex items-center gap-2 mb-2">
            <input type="text" name="options[${optionIndex}][values][${valueIndex}][name]" 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                   placeholder="Value name">
            <input type="number" name="options[${optionIndex}][values][${valueIndex}][price_modifier]" 
                   class="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                   placeholder="0.00" step="0.01">
            <button type="button" onclick="removeOptionValue(this)" class="text-red-600 hover:text-red-800">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', valueHtml);
}

function removeOptionValue(button) {
    button.closest('.flex').remove();
}
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/admin/products/edit.blade.php ENDPATH**/ ?>