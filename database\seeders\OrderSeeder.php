<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = User::where('role', 'customer')->get();
        $products = Product::where('is_active', true)->get();

        if ($customers->isEmpty() || $products->isEmpty()) {
            $this->command->info('No customers or products found. Please run UserSeeder and ProductSeeder first.');
            return;
        }

        $statuses = ['pending', 'confirmed', 'printing', 'completed', 'cancelled'];
        $statusWeights = [0.2, 0.3, 0.2, 0.25, 0.05]; // Probability weights

        // Create orders for the last 30 days
        for ($i = 0; $i < 50; $i++) {
            $customer = $customers->random();
            $product = $products->random();
            
            // Random date within last 30 days
            $createdAt = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));
            
            // Select status based on weights
            $status = $this->weightedRandom($statuses, $statusWeights);
            
            // Calculate total amount (base price + random options)
            $basePrice = $product->base_price;
            $optionsPrice = rand(0, 50); // Random options price
            $quantity = rand(1, 5);
            $unitPrice = $basePrice + $optionsPrice;
            $totalAmount = $unitPrice * $quantity;

            $order = Order::create([
                'user_id' => $customer->id,
                'status' => $status,
                'total_amount' => $totalAmount,
                'notes' => $this->getRandomNotes(),
                'billing_address' => $this->getRandomAddress(),
                'shipping_address' => $this->getRandomAddress(),
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
                'confirmed_at' => in_array($status, ['confirmed', 'printing', 'completed']) ? $createdAt->addHours(rand(1, 6)) : null,
                'completed_at' => $status === 'completed' ? $createdAt->addDays(rand(2, 7)) : null,
            ]);

            // Create order item
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'selected_options' => $this->getRandomOptions(),
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalAmount,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        $this->command->info('Created 50 sample orders with order items.');
    }

    /**
     * Select a random item based on weights
     */
    private function weightedRandom(array $items, array $weights): string
    {
        $totalWeight = array_sum($weights);
        $random = mt_rand(1, $totalWeight * 100) / 100;
        
        $currentWeight = 0;
        foreach ($items as $index => $item) {
            $currentWeight += $weights[$index];
            if ($random <= $currentWeight) {
                return $item;
            }
        }
        
        return $items[0]; // Fallback
    }

    /**
     * Get random order notes
     */
    private function getRandomNotes(): ?string
    {
        $notes = [
            null,
            'Please print in high quality',
            'Rush order - needed ASAP',
            'Customer requested specific color matching',
            'Double-sided printing required',
            'Lamination requested',
            'Special delivery instructions: Call before delivery',
        ];

        return $notes[array_rand($notes)];
    }

    /**
     * Get random address
     */
    private function getRandomAddress(): array
    {
        $addresses = [
            [
                'name' => 'John Doe',
                'phone' => '+233 24 111 1111',
                'address' => '123 Liberation Road, Accra',
                'city' => 'Accra',
                'region' => 'Greater Accra',
            ],
            [
                'name' => 'Jane Smith',
                'phone' => '+233 24 222 2222',
                'address' => '456 Kumasi Street, Kumasi',
                'city' => 'Kumasi',
                'region' => 'Ashanti',
            ],
            [
                'name' => 'Kwame Asante',
                'phone' => '+233 24 333 3333',
                'address' => '789 Cape Coast Road, Cape Coast',
                'city' => 'Cape Coast',
                'region' => 'Central',
            ],
            [
                'name' => 'Ama Serwaa',
                'phone' => '+233 24 444 4444',
                'address' => '321 Tamale Avenue, Tamale',
                'city' => 'Tamale',
                'region' => 'Northern',
            ],
        ];

        return $addresses[array_rand($addresses)];
    }

    /**
     * Get random selected options
     */
    private function getRandomOptions(): array
    {
        $options = [
            ['Size' => 'Medium', 'Color' => 'Black'],
            ['Size' => 'Large', 'Color' => 'White'],
            ['Size' => 'Small', 'Color' => 'Red'],
            ['Size' => 'Extra Large', 'Color' => 'Blue'],
            ['Material' => 'Cotton', 'Print Type' => 'Digital'],
            ['Material' => 'Polyester', 'Print Type' => 'Screen Print'],
            ['Paper Type' => 'Glossy', 'Size' => 'A4'],
            ['Paper Type' => 'Matte', 'Size' => 'A3'],
        ];

        return $options[array_rand($options)];
    }
}
