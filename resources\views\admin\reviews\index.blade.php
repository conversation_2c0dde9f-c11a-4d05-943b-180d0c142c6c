<x-layouts.admin title="Reviews Management">
    <x-slot name="pageTitle">Reviews Management</x-slot>
    <x-slot name="pageDescription">Manage customer reviews displayed on the homepage</x-slot>

    <div class="mb-6 flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <div class="bg-white px-4 py-2 rounded-lg shadow-sm">
                <span class="text-sm text-gray-600">Total Reviews: </span>
                <span class="font-semibold">{{ $reviews->total() }}</span>
            </div>
            <div class="bg-white px-4 py-2 rounded-lg shadow-sm">
                <span class="text-sm text-gray-600">Active: </span>
                <span class="font-semibold text-green-600">{{ $reviews->where('is_active', true)->count() }}</span>
            </div>
            <div class="bg-white px-4 py-2 rounded-lg shadow-sm">
                <span class="text-sm text-gray-600">Featured: </span>
                <span class="font-semibold text-pink-600">{{ $reviews->where('is_featured', true)->count() }}</span>
            </div>
        </div>
        
        <a href="{{ route('admin.reviews.create') }}" 
           class="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
            <i class="fas fa-plus mr-2"></i>Add Review
        </a>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        @if($reviews->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Review</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($reviews as $review)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm mr-3"
                                             style="background-color: {{ $review->avatar_color }}; color: {{ $review->avatar_text_color }}">
                                            {{ $review->avatar_initials }}
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $review->customer_name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate">{{ $review->review_text }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-yellow-400">
                                            {!! $review->star_rating_html !!}
                                        </div>
                                        <span class="ml-2 text-sm text-gray-600">({{ $review->rating }})</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col space-y-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                            {{ $review->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $review->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                        @if($review->is_featured)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-pink-100 text-pink-800">
                                                Featured
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $review->created_at->format('M j, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ route('admin.reviews.show', $review) }}" 
                                           class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.reviews.edit', $review) }}" 
                                           class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ route('admin.reviews.destroy', $review) }}" 
                                              class="inline" onsubmit="return confirm('Are you sure you want to delete this review?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $reviews->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-star text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No reviews yet</h3>
                <p class="text-gray-500 mb-6">Start by adding your first customer review.</p>
                <a href="{{ route('admin.reviews.create') }}" 
                   class="inline-flex items-center px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition">
                    <i class="fas fa-plus mr-2"></i>Add Review
                </a>
            </div>
        @endif
    </div>
</x-layouts.admin>
