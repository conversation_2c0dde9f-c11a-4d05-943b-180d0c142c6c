<x-layouts.admin title="Add New Product">
    <x-slot name="pageTitle">Add New Product</x-slot>
    <x-slot name="pageDescription">Create a new product for your catalog</x-slot>

    <form method="POST" action="{{ route('admin.products.store') }}" enctype="multipart/form-data">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Product Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="Enter product name">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                            <select id="category" name="category" required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="">Select Category</option>
                                <option value="Apparel" {{ old('category') === 'Apparel' ? 'selected' : '' }}>Apparel</option>
                                <option value="Office" {{ old('category') === 'Office' ? 'selected' : '' }}>Office</option>
                                <option value="Giftware" {{ old('category') === 'Giftware' ? 'selected' : '' }}>Giftware</option>
                                <option value="Promotional" {{ old('category') === 'Promotional' ? 'selected' : '' }}>Promotional</option>
                                <option value="Stationery" {{ old('category') === 'Stationery' ? 'selected' : '' }}>Stationery</option>
                                <option value="Signage" {{ old('category') === 'Signage' ? 'selected' : '' }}>Signage</option>
                            </select>
                            @error('category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">Base Price (₵) *</label>
                            <input type="number" id="base_price" name="base_price" value="{{ old('base_price') }}" 
                                   step="0.01" min="0" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="0.00">
                            @error('base_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="description" name="description" rows="4"
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="Enter product description">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Product Image -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Image</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Upload Image</label>
                            <input type="file" id="image" name="image" accept="image/*"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <p class="mt-1 text-sm text-gray-500">Supported formats: JPG, PNG, GIF. Max size: 2MB</p>
                            @error('image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div id="image-preview" class="hidden">
                            <img id="preview-img" src="" alt="Preview" class="w-32 h-32 object-cover rounded-lg border">
                        </div>
                    </div>
                </div>

                <!-- Product Options -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Options</h3>
                    
                    <div id="options-container">
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-plus-circle text-4xl mb-4"></i>
                            <p>Click "Add Option" to create product variations like size, color, material, etc.</p>
                        </div>
                    </div>
                    
                    <button type="button" onclick="addOption()" 
                            class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                        <i class="fas fa-plus mr-2"></i>Add Option
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Product Status -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Status</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active (visible to customers)
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_featured" name="is_featured" value="1"
                                   {{ old('is_featured') ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                Featured product
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Display Settings</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                            <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                                   min="0"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="0">
                            <p class="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
                        </div>
                        
                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">URL Slug</label>
                            <input type="text" id="slug" name="slug" value="{{ old('slug') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="auto-generated">
                            <p class="mt-1 text-sm text-gray-500">Leave blank to auto-generate</p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            <i class="fas fa-save mr-2"></i>Create Product
                        </button>
                        
                        <a href="{{ route('admin.products.index') }}" 
                           class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</x-layouts.admin>

@push('scripts')
<script>
let optionIndex = 0;

// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-img').src = e.target.result;
            document.getElementById('image-preview').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});

// Auto-generate slug from name
document.getElementById('name').addEventListener('input', function(e) {
    const slug = e.target.value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
    document.getElementById('slug').value = slug;
});

function addOption() {
    const container = document.getElementById('options-container');
    
    // Remove empty state if it exists
    if (container.querySelector('.text-center')) {
        container.innerHTML = '';
    }
    
    const optionHtml = `
        <div class="option-group border border-gray-200 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between mb-4">
                <h4 class="font-medium text-gray-900">Option ${optionIndex + 1}</h4>
                <button type="button" onclick="removeOption(this)" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Option Name</label>
                    <input type="text" name="options[${optionIndex}][name]" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                           placeholder="e.g., Size, Color, Material">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Required</label>
                    <select name="options[${optionIndex}][required]" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </div>
            </div>
            
            <div class="option-values">
                <label class="block text-sm font-medium text-gray-700 mb-2">Option Values</label>
                <div class="values-container"></div>
                <button type="button" onclick="addOptionValue(this, ${optionIndex})" 
                        class="mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition text-sm">
                    <i class="fas fa-plus mr-1"></i>Add Value
                </button>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', optionHtml);
    optionIndex++;
}

function removeOption(button) {
    button.closest('.option-group').remove();
    
    // Show empty state if no options left
    const container = document.getElementById('options-container');
    if (container.children.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-plus-circle text-4xl mb-4"></i>
                <p>Click "Add Option" to create product variations like size, color, material, etc.</p>
            </div>
        `;
    }
}

function addOptionValue(button, optionIndex) {
    const container = button.previousElementSibling;
    const valueIndex = container.children.length;
    
    const valueHtml = `
        <div class="flex items-center gap-2 mb-2">
            <input type="text" name="options[${optionIndex}][values][${valueIndex}][name]" 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                   placeholder="Value name">
            <input type="number" name="options[${optionIndex}][values][${valueIndex}][price_modifier]" 
                   class="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                   placeholder="0.00" step="0.01">
            <button type="button" onclick="removeOptionValue(this)" class="text-red-600 hover:text-red-800">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', valueHtml);
}

function removeOptionValue(button) {
    button.closest('.flex').remove();
}
</script>
@endpush
