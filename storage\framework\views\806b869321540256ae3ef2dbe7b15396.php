<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => ['title' => 'Expert Designers - PrintOnline Ghana']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Expert Designers - PrintOnline Ghana']); ?>
    <div class="bg-gray-50 min-h-screen">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Page Header -->
            <div class="text-center mb-16">
                <h1 class="text-4xl font-bold text-gray-900">Meet Our Expert Designers</h1>
                <p class="mt-4 text-lg text-gray-600">Find the perfect designer to bring your vision to life.</p>
            </div>

            <!-- Designers Grid -->
            <?php if($designers->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                    <?php $__currentLoopData = $designers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $designer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="designer-card bg-white rounded-xl shadow-md text-center p-8 transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-lg">
                            <!-- Designer Image -->
                            <img src="<?php echo e($designer->image_url); ?>" 
                                 alt="<?php echo e($designer->name); ?>" 
                                 class="w-32 h-32 rounded-full mx-auto mb-6 object-cover border-4 border-white shadow-lg">
                            
                            <!-- Designer Name -->
                            <h3 class="text-xl font-bold text-gray-900 mb-4"><?php echo e($designer->name); ?></h3>
                            
                            <!-- Designer Bio -->
                            <p class="text-gray-500 mb-6 leading-relaxed"><?php echo e($designer->bio); ?></p>
                            
                            <!-- Social Links and Portfolio -->
                            <div class="flex justify-center items-center gap-4">
                                <?php if($designer->social_links): ?>
                                    <?php $__currentLoopData = $designer->social_links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($url): ?>
                                            <a href="<?php echo e($url); ?>" 
                                               target="_blank" 
                                               class="text-gray-400 hover:text-pink-500 transition-colors duration-200">
                                                <?php switch($platform):
                                                    case ('behance'): ?>
                                                        <i class="fab fa-behance fa-lg"></i>
                                                        <?php break; ?>
                                                    <?php case ('instagram'): ?>
                                                        <i class="fab fa-instagram fa-lg"></i>
                                                        <?php break; ?>
                                                    <?php case ('linkedin'): ?>
                                                        <i class="fab fa-linkedin-in fa-lg"></i>
                                                        <?php break; ?>
                                                    <?php case ('dribbble'): ?>
                                                        <i class="fab fa-dribbble fa-lg"></i>
                                                        <?php break; ?>
                                                    <?php case ('twitter'): ?>
                                                        <i class="fab fa-twitter fa-lg"></i>
                                                        <?php break; ?>
                                                    <?php case ('facebook'): ?>
                                                        <i class="fab fa-facebook-f fa-lg"></i>
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <i class="fas fa-link fa-lg"></i>
                                                <?php endswitch; ?>
                                            </a>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                                
                                <?php if($designer->portfolio_url): ?>
                                    <a href="<?php echo e($designer->portfolio_url); ?>" 
                                       target="_blank" 
                                       class="text-sm font-semibold text-pink-500 hover:text-pink-600 transition-colors duration-200 ml-2">
                                        Portfolio <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <!-- No Designers Found -->
                <div class="text-center py-16">
                    <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No designers available</h3>
                    <p class="text-gray-500 mb-6">
                        We're working on bringing you the best designers. Check back soon!
                    </p>
                </div>
            <?php endif; ?>

            <!-- Call to Action -->
            <div class="mt-16 text-center bg-white rounded-xl p-8 shadow-md">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Need Custom Design Services?</h2>
                <p class="text-gray-600 mb-6">
                    Our expert designers are ready to help bring your ideas to life. 
                    Contact us to discuss your project requirements.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="mailto:<EMAIL>" 
                       class="inline-block bg-pink-500 text-white font-bold py-3 px-8 rounded-lg hover:bg-pink-600 transition duration-300">
                        <i class="fas fa-envelope mr-2"></i>Contact Design Team
                    </a>
                    <a href="tel:+233241234567" 
                       class="inline-block bg-gray-100 text-gray-800 font-bold py-3 px-8 rounded-lg hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-phone mr-2"></i>Call Us
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
    <style>
        .designer-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .designer-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -4px rgba(0,0,0,0.1);
        }
    </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/designers.blade.php ENDPATH**/ ?>