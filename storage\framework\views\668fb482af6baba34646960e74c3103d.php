<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($title ?? 'PrintOnline Ghana - On-Demand Printing & Delivery'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Uppy CSS -->
    <link href="https://releases.transloadit.com/uppy/v3.2.1/uppy.min.css" rel="stylesheet">

    <!-- Tailwind CSS CDN (temporary until Vite build works) -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <style>
        .font-heading {
            font-family: 'Poppins', sans-serif;
        }

        .hero-product-card {
            transition: transform 0.3s ease;
        }

        .hero-product-card:hover {
            transform: scale(1.05) !important;
        }

        .reviews-slider {
            overflow: hidden;
            position: relative;
        }

        .reviews-track {
            display: flex;
            animation: scroll 30s linear infinite;
            gap: 1.5rem;
        }

        .review-card {
            min-width: 300px;
            background: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        @keyframes scroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
        }

        .product-card {
            background: white;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-4px);
        }

        .product-image-wrapper {
            position: relative;
            overflow: hidden;
        }

        .card-body {
            padding: 1rem;
        }

        .step-number {
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
        }
    </style>

    <script>
        // Basic Alpine.js functionality can be added here if needed
    </script>


    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="text-gray-800">
    <!-- Top Bar -->
    <div class="top-bar bg-white">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-2 text-xs text-gray-600">
            <span>Every time. Any reason. Or we'll make it right.</span>
            <span>Call for support: <strong>+233 24 123 4567</strong></span>
        </div>
    </div>

    <!-- Header -->
    <header class="sticky top-0 z-50 shadow-sm bg-white">
        <nav class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="<?php echo e(route('home')); ?>">
                    <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" 
                         alt="PrintOnline Ghana Logo" class="h-10 mr-3">
                </a>
            </div>

            <!-- Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="<?php echo e(route('home')); ?>" class="text-gray-600 hover:text-pink-500 font-semibold <?php echo e(request()->routeIs('home') ? 'text-pink-500' : ''); ?>">HOME</a>
                <a href="<?php echo e(route('shop')); ?>" class="text-gray-600 hover:text-pink-500 font-semibold <?php echo e(request()->routeIs('shop') ? 'text-pink-500' : ''); ?>">SHOP</a>
                <a href="<?php echo e(route('designers')); ?>" class="text-gray-600 hover:text-pink-500 font-semibold <?php echo e(request()->routeIs('designers') ? 'text-pink-500' : ''); ?>">EXPERT DESIGNERS</a>
                <a href="<?php echo e(route('home')); ?>#how-it-works-section" class="text-gray-600 hover:text-pink-500 font-semibold">HOW IT WORKS</a>
            </div>

            <!-- Account Section -->
            <div class="flex items-center gap-6">
                <?php if(auth()->guard()->check()): ?>
                    <!-- User is logged in -->
                    <div class="relative group">
                        <div class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer">
                            <i class="fas fa-user-circle text-gray-600 text-xl"></i>
                            <span class="text-sm font-semibold text-gray-700"><?php echo e(Auth::user()->name); ?></span>
                            <i class="fas fa-chevron-down text-gray-500 text-xs ml-1"></i>
                        </div>

                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <?php if(Auth::user()->isAdmin()): ?>
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-tachometer-alt mr-2"></i>Admin Dashboard
                                </a>
                            <?php endif; ?>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i>My Profile
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-shopping-bag mr-2"></i>My Orders
                            </a>
                            <div class="border-t border-gray-100"></div>
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- User is not logged in -->
                    <div class="flex items-center gap-3">
                        <a href="<?php echo e(route('login')); ?>" class="text-gray-600 hover:text-pink-500 font-semibold">
                            Login
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="bg-pink-500 text-white px-4 py-2 rounded-full font-semibold hover:bg-pink-600 transition">
                            Sign Up
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <?php echo e($slot); ?>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="lg:col-span-1">
                    <div class="flex items-center">
                        <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" 
                             alt="PrintOnline Ghana Logo" class="h-10 filter grayscale brightness-0 invert">
                    </div>
                    <p class="mt-4 text-gray-400 text-sm">
                        The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold tracking-wider uppercase">Quick Links</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="<?php echo e(route('home')); ?>" class="text-base text-gray-400 hover:text-white transition">Home</a></li>
                        <li><a href="<?php echo e(route('shop')); ?>?category=Apparel" class="text-base text-gray-400 hover:text-white transition">Apparel</a></li>
                        <li><a href="<?php echo e(route('shop')); ?>?category=Office" class="text-base text-gray-400 hover:text-white transition">Office</a></li>
                        <li><a href="<?php echo e(route('shop')); ?>?category=Giftware" class="text-base text-gray-400 hover:text-white transition">Gifts</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-sm font-semibold tracking-wider uppercase">Contact Us</h3>
                    <ul class="mt-4 space-y-3 text-gray-400 text-sm">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 flex-shrink-0"></i>
                            <span>123 Oxford Street, Osu, Accra, Ghana</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3"></i>
                            <span>+233 24 123 4567</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3"></i>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>

                <!-- Social Media -->
                <div>
                    <h3 class="text-sm font-semibold tracking-wider uppercase">Follow Us</h3>
                    <div class="flex mt-4 space-x-6">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-facebook-f fa-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-instagram fa-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-twitter fa-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-linkedin-in fa-lg"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400">
                <p>&copy; <?php echo e(date('Y')); ?> PrintOnline Ghana. All rights reserved.</p>
            </div>
        </div>
    </footer>

    
    <?php echo $__env->make('components.auth-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Livewire Scripts (temporarily commented out) -->
    

    <!-- External Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://releases.transloadit.com/uppy/v3.2.1/uppy.min.js"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <script>
        // Basic JavaScript functionality
        console.log('PrintOnline Ghana - Home page loaded');

        // Auth Modal Functions
        function showAuthModal() {
            document.getElementById('auth-modal').classList.remove('hidden');
        }

        function hideAuthModal() {
            document.getElementById('auth-modal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('auth-modal');
            if (event.target === modal) {
                hideAuthModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hideAuthModal();
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\Printshop\resources\views/components/layouts/app.blade.php ENDPATH**/ ?>