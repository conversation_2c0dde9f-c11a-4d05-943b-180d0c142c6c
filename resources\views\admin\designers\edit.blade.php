<x-layouts.admin title="Edit Designer">
    <x-slot name="pageTitle">Edit Designer: {{ $designer->name }}</x-slot>
    <x-slot name="pageDescription">Update designer profile information</x-slot>

    <form method="POST" action="{{ route('admin.designers.update', $designer) }}" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Designer Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                    
                    <div class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Designer Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name', $designer->name) }}" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="Enter designer name">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Bio *</label>
                            <textarea id="bio" name="bio" rows="4" required
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                      placeholder="Enter designer bio and specialties...">{{ old('bio', $designer->bio) }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">Maximum 500 characters</p>
                            @error('bio')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="portfolio_url" class="block text-sm font-medium text-gray-700 mb-2">Portfolio URL</label>
                            <input type="url" id="portfolio_url" name="portfolio_url" value="{{ old('portfolio_url', $designer->portfolio_url) }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://example.com/portfolio">
                            @error('portfolio_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Designer Image -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Designer Image</h3>
                    
                    <div class="space-y-4">
                        @if($designer->image_url)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Current Image</label>
                                <img src="{{ $designer->image_url }}" alt="{{ $designer->name }}" 
                                     class="w-32 h-32 object-cover rounded-lg border">
                            </div>
                        @endif
                        
                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $designer->image_url ? 'Replace Image' : 'Upload Image' }}
                            </label>
                            <input type="file" id="image" name="image" accept="image/*"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <p class="mt-1 text-sm text-gray-500">Supported formats: JPG, PNG, GIF. Max size: 2MB. Recommended: 400x400px</p>
                            @error('image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div id="image-preview" class="hidden">
                            <img id="preview-img" src="" alt="Preview" class="w-32 h-32 object-cover rounded-lg border">
                        </div>
                    </div>
                </div>

                <!-- Social Links -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Social Links</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="behance" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-behance mr-2 text-blue-600"></i>Behance
                            </label>
                            <input type="url" id="behance" name="social_links[behance]" value="{{ old('social_links.behance', $designer->social_links['behance'] ?? '') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://behance.net/username">
                        </div>
                        
                        <div>
                            <label for="dribbble" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-dribbble mr-2 text-pink-600"></i>Dribbble
                            </label>
                            <input type="url" id="dribbble" name="social_links[dribbble]" value="{{ old('social_links.dribbble', $designer->social_links['dribbble'] ?? '') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://dribbble.com/username">
                        </div>
                        
                        <div>
                            <label for="instagram" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-instagram mr-2 text-purple-600"></i>Instagram
                            </label>
                            <input type="url" id="instagram" name="social_links[instagram]" value="{{ old('social_links.instagram', $designer->social_links['instagram'] ?? '') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://instagram.com/username">
                        </div>
                        
                        <div>
                            <label for="linkedin" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fab fa-linkedin mr-2 text-blue-700"></i>LinkedIn
                            </label>
                            <input type="url" id="linkedin" name="social_links[linkedin]" value="{{ old('social_links.linkedin', $designer->social_links['linkedin'] ?? '') }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                                   placeholder="https://linkedin.com/in/username">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Designer Status -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Designer Status</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" 
                                   {{ old('is_active', $designer->is_active) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active (visible on designers page)
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Display Settings -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Display Settings</h3>
                    
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                        <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', $designer->sort_order) }}" 
                               min="0"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        <p class="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="space-y-3">
                        <button type="submit" 
                                class="w-full px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                            <i class="fas fa-save mr-2"></i>Update Designer
                        </button>
                        
                        <a href="{{ route('admin.designers.show', $designer) }}" 
                           class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition text-center">
                            <i class="fas fa-eye mr-2"></i>View Designer
                        </a>
                        
                        <a href="{{ route('admin.designers.index') }}" 
                           class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition text-center">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Designers
                        </a>
                        
                        <form method="POST" action="{{ route('admin.designers.destroy', $designer) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this designer?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition">
                                <i class="fas fa-trash mr-2"></i>Delete Designer
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </form>
</x-layouts.admin>

@push('scripts')
<script>
// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-img').src = e.target.result;
            document.getElementById('image-preview').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endpush
