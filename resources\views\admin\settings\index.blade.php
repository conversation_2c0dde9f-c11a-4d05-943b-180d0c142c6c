<x-layouts.admin title="Settings">
    <x-slot name="pageTitle">Settings</x-slot>
    <x-slot name="pageDescription">Configure your print shop settings and preferences</x-slot>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Settings Navigation -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Settings Categories</h3>
                <nav class="space-y-2">
                    <a href="#general" onclick="showSection('general', this)"
                       class="settings-nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg text-pink-600 bg-pink-50">
                        <i class="fas fa-cog mr-3"></i>General Settings
                    </a>
                    <a href="#business" onclick="showSection('business', this)"
                       class="settings-nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-600 hover:text-pink-600 hover:bg-pink-50">
                        <i class="fas fa-building mr-3"></i>Business Information
                    </a>
                    <a href="#email" onclick="showSection('email', this)"
                       class="settings-nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-600 hover:text-pink-600 hover:bg-pink-50">
                        <i class="fas fa-envelope mr-3"></i>Email Settings
                    </a>
                    <a href="#payment" onclick="showSection('payment', this)"
                       class="settings-nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-600 hover:text-pink-600 hover:bg-pink-50">
                        <i class="fas fa-credit-card mr-3"></i>Payment Settings
                    </a>
                    <a href="#shipping" onclick="showSection('shipping', this)"
                       class="settings-nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-600 hover:text-pink-600 hover:bg-pink-50">
                        <i class="fas fa-truck mr-3"></i>Shipping Settings
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="lg:col-span-2">
            <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
                @csrf
                
                <!-- General Settings -->
                <div id="general-section" class="settings-section bg-white rounded-xl shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">General Settings</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                            <input type="text" id="site_name" name="site_name" 
                                   value="{{ $settings['site_name'] ?? 'PrintOnline Ghana' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="site_tagline" class="block text-sm font-medium text-gray-700 mb-2">Site Tagline</label>
                            <input type="text" id="site_tagline" name="site_tagline" 
                                   value="{{ $settings['site_tagline'] ?? 'On-Demand Printing & Delivery' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                            <textarea id="site_description" name="site_description" rows="3"
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">{{ $settings['site_description'] ?? 'The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.' }}</textarea>
                        </div>
                        
                        <div>
                            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                            <select id="currency" name="currency" 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="GHS" {{ ($settings['currency'] ?? 'GHS') === 'GHS' ? 'selected' : '' }}>Ghana Cedi (₵)</option>
                                <option value="USD" {{ ($settings['currency'] ?? 'GHS') === 'USD' ? 'selected' : '' }}>US Dollar ($)</option>
                                <option value="EUR" {{ ($settings['currency'] ?? 'GHS') === 'EUR' ? 'selected' : '' }}>Euro (€)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                            <select id="timezone" name="timezone" 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                <option value="Africa/Accra" {{ ($settings['timezone'] ?? 'Africa/Accra') === 'Africa/Accra' ? 'selected' : '' }}>Africa/Accra</option>
                                <option value="UTC" {{ ($settings['timezone'] ?? 'Africa/Accra') === 'UTC' ? 'selected' : '' }}>UTC</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Business Information -->
                <div id="business-section" class="settings-section bg-white rounded-xl shadow-md p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Business Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="business_name" class="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                            <input type="text" id="business_name" name="business_name" 
                                   value="{{ $settings['business_name'] ?? 'PrintOnline Ghana' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="business_phone" class="block text-sm font-medium text-gray-700 mb-2">Business Phone</label>
                            <input type="text" id="business_phone" name="business_phone" 
                                   value="{{ $settings['business_phone'] ?? '+233 24 123 4567' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="business_email" class="block text-sm font-medium text-gray-700 mb-2">Business Email</label>
                            <input type="email" id="business_email" name="business_email" 
                                   value="{{ $settings['business_email'] ?? '<EMAIL>' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="business_website" class="block text-sm font-medium text-gray-700 mb-2">Website</label>
                            <input type="url" id="business_website" name="business_website" 
                                   value="{{ $settings['business_website'] ?? 'https://printonlinegh.com' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="business_address" class="block text-sm font-medium text-gray-700 mb-2">Business Address</label>
                            <textarea id="business_address" name="business_address" rows="3"
                                      class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">{{ $settings['business_address'] ?? '123 Oxford Street, Osu, Accra, Ghana' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Email Settings -->
                <div id="email-section" class="settings-section bg-white rounded-xl shadow-md p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Email Settings</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                            <input type="text" id="mail_from_name" name="mail_from_name" 
                                   value="{{ $settings['mail_from_name'] ?? 'PrintOnline Ghana' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                            <input type="email" id="mail_from_address" name="mail_from_address" 
                                   value="{{ $settings['mail_from_address'] ?? '<EMAIL>' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="email_notifications" name="email_notifications" value="1"
                                       {{ ($settings['email_notifications'] ?? true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                                <label for="email_notifications" class="ml-2 block text-sm text-gray-900">
                                    Enable email notifications for new orders
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Settings -->
                <div id="payment-section" class="settings-section bg-white rounded-xl shadow-md p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Payment Settings</h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="enable_paystack" name="enable_paystack" value="1"
                                   {{ ($settings['enable_paystack'] ?? false) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="enable_paystack" class="ml-2 block text-sm text-gray-900">
                                Enable Paystack payments
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="enable_momo" name="enable_momo" value="1"
                                   {{ ($settings['enable_momo'] ?? false) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="enable_momo" class="ml-2 block text-sm text-gray-900">
                                Enable Mobile Money payments
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="enable_bank_transfer" name="enable_bank_transfer" value="1"
                                   {{ ($settings['enable_bank_transfer'] ?? true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded">
                            <label for="enable_bank_transfer" class="ml-2 block text-sm text-gray-900">
                                Enable bank transfer payments
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Shipping Settings -->
                <div id="shipping-section" class="settings-section bg-white rounded-xl shadow-md p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Shipping Settings</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="shipping_fee_accra" class="block text-sm font-medium text-gray-700 mb-2">Shipping Fee - Accra (₵)</label>
                            <input type="number" id="shipping_fee_accra" name="shipping_fee_accra" step="0.01"
                                   value="{{ $settings['shipping_fee_accra'] ?? '15.00' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="shipping_fee_other" class="block text-sm font-medium text-gray-700 mb-2">Shipping Fee - Other Cities (₵)</label>
                            <input type="number" id="shipping_fee_other" name="shipping_fee_other" step="0.01"
                                   value="{{ $settings['shipping_fee_other'] ?? '25.00' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="free_shipping_threshold" class="block text-sm font-medium text-gray-700 mb-2">Free Shipping Threshold (₵)</label>
                            <input type="number" id="free_shipping_threshold" name="free_shipping_threshold" step="0.01"
                                   value="{{ $settings['free_shipping_threshold'] ?? '100.00' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                        
                        <div>
                            <label for="processing_time" class="block text-sm font-medium text-gray-700 mb-2">Processing Time (days)</label>
                            <input type="number" id="processing_time" name="processing_time" min="1"
                                   value="{{ $settings['processing_time'] ?? '3' }}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="flex justify-end">
                    <button type="submit" class="px-8 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition font-semibold">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</x-layouts.admin>

@push('scripts')
<script>
function showSection(sectionName, clickedElement) {
    // Prevent default link behavior
    event.preventDefault();

    // Hide all sections
    document.querySelectorAll('.settings-section').forEach(section => {
        section.classList.add('hidden');
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.remove('hidden');
    }

    // Update navigation
    document.querySelectorAll('.settings-nav-link').forEach(link => {
        link.classList.remove('text-pink-600', 'bg-pink-50');
        link.classList.add('text-gray-600');
    });

    // Highlight clicked element
    if (clickedElement) {
        clickedElement.classList.remove('text-gray-600');
        clickedElement.classList.add('text-pink-600', 'bg-pink-50');
    }
}

// Initialize the page with general section visible
document.addEventListener('DOMContentLoaded', function() {
    // Hide all sections except general
    document.querySelectorAll('.settings-section').forEach(section => {
        if (section.id !== 'general-section') {
            section.classList.add('hidden');
        }
    });
});

// Form submission with loading state
document.querySelector('form').addEventListener('submit', function(e) {
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;

    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';

    // Re-enable button after 5 seconds as fallback
    setTimeout(() => {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    }, 5000);
});
</script>
@endpush
